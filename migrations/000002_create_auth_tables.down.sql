-- Drop triggers first
DROP TRIGGER IF EXISTS update_permissions_updated_at_trigger ON permissions;
DROP TRIGGER IF EXISTS update_roles_updated_at_trigger ON roles;

-- Drop trigger functions
DROP FUNCTION IF EXISTS update_permissions_updated_at();
DROP FUNCTION IF EXISTS update_roles_updated_at();

-- Drop indexes
DROP INDEX IF EXISTS idx_role_permissions_role_id;
DROP INDEX IF EXISTS idx_account_roles_company_id;
DROP INDEX IF EXISTS idx_account_roles_account_id;

-- Drop tables in reverse order (respecting foreign key constraints)
DROP TABLE IF EXISTS account_roles;
DROP TABLE IF EXISTS role_permissions;
DROP TABLE IF EXISTS permissions;
DROP TABLE IF EXISTS roles;

-- Remove password_hash column from accounts table
ALTER TABLE accounts DROP COLUMN IF EXISTS password_hash;
-- Drop triggers
DROP TRIGGER IF EXISTS update_ledger_transactions_updated_at ON ledger_transactions;
DROP TRIGGER IF EXISTS update_payment_methods_updated_at ON payment_methods;
DROP TRIGGER IF EXISTS update_accounts_updated_at ON accounts;

-- Drop function
DROP FUNCTION IF EXISTS update_updated_at_column();

-- Drop indexes
DROP INDEX IF EXISTS idx_ledger_entries_created_at;
DROP INDEX IF EXISTS idx_ledger_entries_type;
DROP INDEX IF EXISTS idx_ledger_entries_account_id;
DROP INDEX IF EXISTS idx_ledger_entries_transaction_id;

DROP INDEX IF EXISTS idx_ledger_transactions_created_at;
DROP INDEX IF EXISTS idx_ledger_transactions_status;
DROP INDEX IF EXISTS idx_ledger_transactions_type;
DROP INDEX IF EXISTS idx_ledger_transactions_reference_id;

DROP INDEX IF EXISTS idx_payment_methods_default;
DROP INDEX IF EXISTS idx_payment_methods_type;
DROP INDEX IF EXISTS idx_payment_methods_account_id;

DROP INDEX IF EXISTS idx_accounts_active;
DROP INDEX IF EXISTS idx_accounts_type;
DROP INDEX IF EXISTS idx_accounts_email;

-- Drop tables (in reverse order due to foreign key constraints)
DROP TABLE IF EXISTS ledger_entries;
DROP TABLE IF EXISTS ledger_transactions;
DROP TABLE IF EXISTS payment_methods;
DROP TABLE IF EXISTS accounts;

-- Drop extension
DROP EXTENSION IF EXISTS "uuid-ossp";

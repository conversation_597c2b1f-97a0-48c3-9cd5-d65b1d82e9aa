-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create accounts table
CREATE TABLE accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    type VA<PERSON><PERSON><PERSON>(50) NOT NULL CHECK (type IN ('personal', 'business')),
    balance BIGINT NOT NULL DEFAULT 0, -- Amount in smallest currency unit (e.g., cents)
    currency_code VARCHAR(3) NOT NULL DEFAULT 'USD',
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create payment_methods table
CREATE TABLE payment_methods (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL CHECK (type IN ('credit_card', 'debit_card', 'bank_account', 'digital_wallet')),
    provider VARCHAR(100) NOT NULL, -- e.g., 'visa', 'mastercard', 'paypal'
    last_four VARCHAR(4), -- Last 4 digits for cards
    expiry_month INTEGER, -- For cards
    expiry_year INTEGER, -- For cards
    is_default BOOLEAN NOT NULL DEFAULT false,
    is_active BOOLEAN NOT NULL DEFAULT true,
    metadata JSONB, -- Store provider-specific data
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    deleted_at TIMESTAMP WITH TIME ZONE
);

-- Create ledger_transactions table
CREATE TABLE ledger_transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    reference_id VARCHAR(255) UNIQUE NOT NULL, -- External reference (order ID, payment ID, etc.)
    type VARCHAR(50) NOT NULL CHECK (type IN ('payment', 'refund', 'transfer', 'fee', 'adjustment')),
    status VARCHAR(50) NOT NULL CHECK (status IN ('pending', 'completed', 'failed', 'cancelled')),
    amount BIGINT NOT NULL, -- Total transaction amount in smallest currency unit
    currency_code VARCHAR(3) NOT NULL DEFAULT 'USD',
    description TEXT,
    metadata JSONB, -- Store transaction-specific data
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create ledger_entries table (double-entry bookkeeping)
CREATE TABLE ledger_entries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_id UUID NOT NULL REFERENCES ledger_transactions(id) ON DELETE CASCADE,
    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    type VARCHAR(10) NOT NULL CHECK (type IN ('debit', 'credit')),
    amount BIGINT NOT NULL, -- Amount in smallest currency unit
    currency_code VARCHAR(3) NOT NULL DEFAULT 'USD',
    balance_after BIGINT NOT NULL, -- Account balance after this entry
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_accounts_email ON accounts(email) WHERE deleted_at IS NULL;
CREATE INDEX idx_accounts_type ON accounts(type) WHERE deleted_at IS NULL;
CREATE INDEX idx_accounts_active ON accounts(is_active) WHERE deleted_at IS NULL;

CREATE INDEX idx_payment_methods_account_id ON payment_methods(account_id) WHERE deleted_at IS NULL;
CREATE INDEX idx_payment_methods_type ON payment_methods(type) WHERE deleted_at IS NULL;
CREATE INDEX idx_payment_methods_default ON payment_methods(account_id, is_default) WHERE deleted_at IS NULL AND is_default = true;

CREATE INDEX idx_ledger_transactions_reference_id ON ledger_transactions(reference_id);
CREATE INDEX idx_ledger_transactions_type ON ledger_transactions(type);
CREATE INDEX idx_ledger_transactions_status ON ledger_transactions(status);
CREATE INDEX idx_ledger_transactions_created_at ON ledger_transactions(created_at);

CREATE INDEX idx_ledger_entries_transaction_id ON ledger_entries(transaction_id);
CREATE INDEX idx_ledger_entries_account_id ON ledger_entries(account_id);
CREATE INDEX idx_ledger_entries_type ON ledger_entries(type);
CREATE INDEX idx_ledger_entries_created_at ON ledger_entries(created_at);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_accounts_updated_at BEFORE UPDATE ON accounts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payment_methods_updated_at BEFORE UPDATE ON payment_methods
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_ledger_transactions_updated_at BEFORE UPDATE ON ledger_transactions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Seed data for auth tables
-- Following Rule 9.2: Create seed file corresponding to auth migration
-- Note: Basic roles and permissions are already created by the migration
-- This file adds role-permission mappings using the existing data

-- The migration already creates these roles:
-- owner, admin, manager, employee, viewer, finance, support
-- And these permissions:
-- payment:create, payment:read, payment:update, payment:delete
-- account:create, account:read, account:update, account:delete
-- And others...

-- Create role-permission mappings using existing roles and permissions
-- This uses dynamic queries to map roles to permissions by name
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE (r.name = 'admin' AND p.name IN (
    'payment:create', 'payment:read', 'payment:update', 'payment:delete',
    'account:create', 'account:read', 'account:update', 'account:delete'
))
OR (r.name = 'owner' AND p.name IN (
    'payment:create', 'payment:read', 'payment:update', 'payment:delete',
    'account:create', 'account:read', 'account:update', 'account:delete'
))
OR (r.name = 'manager' AND p.name IN (
    'payment:read', 'payment:update',
    'account:read', 'account:update'
))
OR (r.name = 'finance' AND p.name IN (
    'payment:create', 'payment:read', 'payment:update',
    'account:read'
))
OR (r.name = 'employee' AND p.name IN (
    'payment:read', 'account:read'
))
OR (r.name = 'viewer' AND p.name IN (
    'payment:read', 'account:read'
))
ON CONFLICT (role_id, permission_id) DO NOTHING;

.PHONY: help build run test clean docker-up docker-down migrate-up migrate-down sqlc-generate

# Default target
help: ## Show this help message
	@echo 'Usage: make [target]'
	@echo ''
	@echo 'Targets:'
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Build the application
build: ## Build the application binary
	@echo "Building application..."
	@mkdir -p bin
	@go build -o bin/server ./cmd/server

# Run the application
run: build ## Run the application
	@echo "Starting application..."
	@./bin/server

# Run tests
test: ## Run tests
	@echo "Running tests..."
	@go test -v ./...

# Run tests with coverage
test-coverage: ## Run tests with coverage
	@echo "Running tests with coverage..."
	@go test -v -cover ./...

# Clean build artifacts
clean: ## Clean build artifacts
	@echo "Cleaning..."
	@rm -rf bin/
	@go clean

# Generate database code with sqlc
sqlc-generate: ## Generate database code with sqlc
	@echo "Generating database code..."
	@sqlc generate

# Docker commands
docker-up: ## Start all services with docker-compose
	@echo "Starting services with docker-compose..."
	@docker-compose up -d

docker-down: ## Stop all services
	@echo "Stopping services..."
	@docker-compose down

docker-logs: ## View application logs
	@docker-compose logs -f app

# Database migration commands
migrate-up: ## Run database migrations up
	@echo "Running migrations up..."
	@migrate -path migrations -database "postgres://payment_user:payment_password@localhost:5432/payment_db?sslmode=disable" up

migrate-down: ## Run database migrations down (1 step)
	@echo "Running migrations down..."
	@migrate -path migrations -database "postgres://payment_user:payment_password@localhost:5432/payment_db?sslmode=disable" down 1

migrate-force: ## Force migration version (use with VERSION=N)
	@echo "Forcing migration version $(VERSION)..."
	@migrate -path migrations -database "postgres://payment_user:payment_password@localhost:5432/payment_db?sslmode=disable" force $(VERSION)

# Development setup
dev-setup: ## Set up development environment
	@echo "Setting up development environment..."
	@cp configs/config.example.yaml configs/config.yaml
	@echo "Created configs/config.yaml from example"
	@echo "Please edit configs/config.yaml with your settings"

# Install development tools
install-tools: ## Install development tools
	@echo "Installing development tools..."
	@go install -tags 'postgres' github.com/golang-migrate/migrate/v4/cmd/migrate@latest
	@go install github.com/sqlc-dev/sqlc/cmd/sqlc@latest
	@echo "Tools installed successfully"

# Lint code
lint: ## Run linter
	@echo "Running linter..."
	@golangci-lint run

# Format code
fmt: ## Format code
	@echo "Formatting code..."
	@go fmt ./...

# Tidy dependencies
tidy: ## Tidy go modules
	@echo "Tidying dependencies..."
	@go mod tidy

# Full development cycle
dev: clean sqlc-generate build test ## Clean, generate, build and test

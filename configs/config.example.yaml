server:
  host: "0.0.0.0"
  port: 8080
  mode: "development" # development, production
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 60s

logger:
  level: "info" # debug, info, warn, error
  mode: "development" # development, production

database:
  host: "localhost"
  port: 5432
  user: "payment_user"
  password: "payment_password"
  name: "payment_db"
  ssl_mode: "disable"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300s

redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  max_retries: 3
  pool_size: 10
  min_idle_conns: 5

jwt:
  secret: "your-super-secret-jwt-key-change-this-in-production"
  access_token_duration: 15m
  refresh_token_duration: 720h # 30 days
  issuer: "payment-service-v2"

sentry:
  dsn: "" # Add your Sentry DSN here
  environment: "development"
  traces_sample_rate: 1.0

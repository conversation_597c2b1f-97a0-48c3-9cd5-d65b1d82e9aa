# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Configuration files with sensitive data
configs/config.yaml
configs/config.local.yaml
# Keep the example file
!configs/config.example.yaml

# Log files
*.log
logs/

# Database files
*.db
*.sqlite
*.sqlite3

# Docker files
.dockerignore

# Build artifacts
dist/
build/
bin/

# Temporary files
tmp/
temp/

# Coverage reports
coverage.out
coverage.html

# Generated code (keep this if you want to commit generated code)
# internal/persistence/db/

# Migration files (if you want to ignore them)
# migrations/

# Vendor directory
vendor/

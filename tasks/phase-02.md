Phase 2 Development Guide: Authentication & User Management
Goal: Implement a complete, secure, and multi-tenant user registration and login system, including password hashing, JWT generation, and Redis-backed session/refresh token management.

Task 1: Domain Modeling for Multi-Tenant Users and Roles
Refine the Account Aggregate:

File: internal/account/account.go

Action: Ensure the Account struct can represent both individual users (USER type) and the companies they belong to (COMPANY type). Implement the SetPassword and CheckPassword methods using golang.org/x/crypto/argon2 for secure password handling on USER accounts.

Define Role and Permission Entities:

File: internal/account/role.go

Action: Define Role and Permission structs as previously discussed. This lays the groundwork for RBAC.

Task 2: Multi-Tenant Database Schema and Queries
Install Latest Go Packages:

Action: Ensure you are using the latest stable versions of core libraries.

go get github.com/golang-jwt/jwt/v5@latest
go get github.com/redis/go-redis/v9@latest
go get golang.org/x/crypto@latest

Create Migration for Multi-Tenant Auth:

Action: Use golang-migrate to create a new migration. The key change is adding company_id to the account_roles table to enforce tenancy at the database level.

SQL (migrations/000002_create_auth_tables.up.sql):

ALTER TABLE accounts ADD COLUMN password_hash VARCHAR(255); 

CREATE TABLE roles (

    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL

); 

CREATE TABLE permissions (

    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) UNIQUE NOT NULL

); 

CREATE TABLE role_permissions (

    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    PRIMARY KEY (role_id, permission_id)

); 

-- This table now links a user to a specific role WITHIN a specific company.
CREATE TABLE account_roles (

    account_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    company_id UUID NOT NULL REFERENCES accounts(id) ON DELETE CASCADE,
    PRIMARY KEY (account_id, role_id, company_id)

); 

Write sqlc Queries:

File: internal/persistence/queries/account.sql

Action: Add queries to find a user's roles for a specific company and to fetch their permissions via roles.

-- name: GetAccountRolesForCompany :many
SELECT r.* FROM roles r
JOIN account_roles ar ON r.id = ar.role_id
WHERE ar.account_id = $1 AND ar.company_id = $2; 

-- name: GetPermissionsForRole :many
SELECT p.name FROM permissions p
JOIN role_permissions rp ON p.id = rp.permission_id
WHERE rp.role_id = $1; 

Action: Run sqlc generate.

Task 3: Application Service with Redis-Backed JWTs
Implement the UserRepository:

File: internal/persistence/postgres/user_repository.go

Action: Implement the account. Repository interface using the sqlc-generated code.

Define and Implement the AuthService:

File: internal/account/service.go

Action: The AuthService will now require a *redis. Client in its constructor.

Implement Redis-Backed JWT and Refresh Token Logic:

Action (Login method):

After validating the user's password, generate two tokens:

Access Token (JWT): A short-lived JWT (e.g., 15 minutes) containing sub (user ID), exp, iat, and a jti (JWT ID, a unique UUID). It will also contain custom claims for company_id and roles.

Refresh Token: A cryptographically secure random string (e.g., crypto/rand). Do not use a JWT for the refresh token.

Store Refresh Token in Redis:

Key: refresh_token:<refresh_token_string>

Value: The user's account ID.

TTL: A long duration, e.g., 30 * 24 * time. Hour.

Store Access Token JTI in Redis (for session control):

Key: session:<jti_from_jwt>

Value: The user's account ID.

TTL: The same duration as the access token's expiration (e.g., 15 * time. Minute).

Return both the access token and refresh token to the client.

Action (New RefreshToken method):

Create a new service method RefreshToken(refreshToken string).

It will look up the provided refreshToken in Redis. If it exists, it means the session is valid.

Token Rotation: Delete the old refresh token from Redis.

Generate a new pair of access and refresh tokens and store them in Redis, just like in the Login method.

Return the new tokens.

Task 4: API Layer with Multi-Tenancy Enforcement
Create Auth Handlers:

File: internal/platform/server/handlers/auth_handler.go

Action: Create handlers for Login, Register, and a new Refresh endpoint that calls the authService. RefreshToken method.

Implement Authentication and Tenancy Middleware:

File: internal/platform/server/middleware/auth.go

Action (Auth Middleware):

Validate the JWT's signature and expiration.

Crucially, it must also check if the token's jti claim exists as a key in Redis. If the key is missing, the token has expired or been revoked, and the request must be rejected with a 401.

On success, add userID, companyID, and roles from the token's claims to the Gin context.

Action (Tenancy Middleware - Optional but Recommended): Create a separate middleware that runs after the auth middleware. It ensures that the companyID from the JWT matches any companyID specified in the URL path (e.g., in /v1/companies/:company_id/payments). This provides an extra layer of defense against authorization bypass.

Wire Up Routes:

File: cmd/server/main.go

Action:

Create a public /auth group with /login, /register, and /refresh endpoints.

Create a protected /v1 group using the new AuthMiddleware.

Add a sample protected endpoint (/v1/me) that returns the user information stored in the Gin context.

SYSTEM PROMPT: You are a senior Go developer. You will execute the following tasks sequentially to build the authentication and user management module. You MUST strictly adhere to the CODING_GUIDELINES_V1.1 ruleset for all code generation.

TASK 1: DOMAIN AND DATABASE LAYER

1.1. MODIFY FILE: internal/account/account.go. Add a passwordHash string field to the Account struct. Implement two methods: SetPassword(plainTextPassword string) error using golang.org/x/crypto/argon2, and CheckPassword(plainTextPassword string) bool.
1.2. CREATE FILE: internal/account/role.go. Define Role and Permission structs.
1.3. RUN COMMAND: go get github.com/golang-jwt/jwt/v5@latest, go get github.com/redis/go-redis/v9@latest, go get golang.org/x/crypto@latest.
1.4. RUN COMMAND: migrate create -ext sql -dir migrations -seq create_auth_tables.
1.5. WRITE TO FILE: migrations/000002_create_auth_tables.up.sql. Add an ALTER TABLE accounts ADD COLUMN password_hash VARCHAR(255); . Add CREATE TABLE statements for roles, permissions, role_permissions. Create the account_roles table with a composite primary key: PRIMARY KEY (account_id, role_id, company_id). All foreign keys MUST have an ON DELETE CASCADE policy.
1.6. WRITE TO FILE: migrations/000002_create_auth_tables.down.sql with corresponding DROP TABLE and ALTER TABLE ... DROP COLUMN statements.
1.7. MODIFY FILE: internal/persistence/queries/account.sql. Add sqlc queries: CreateUserAccount, GetAccountByEmail, AssignRoleToAccount, GetAccountRolesForCompany, GetPermissionsForRole.
1.8. RUN COMMAND: sqlc generate.

TASK 2: APPLICATION AND INFRASTRUCTURE LAYER

2.1. MODIFY FILE: internal/platform/config/config.go. Add a Redis struct with Host, Port, Password, and DB fields to the main Config struct. Add a JWT struct with a Secret field.
2.2. CREATE FILE: internal/platform/redis/redis.go. Create a NewClient(cfg *config. Config) *redis. Client function that returns a new go-redis client.
2.3. CREATE FILE: internal/persistence/postgres/account_repository.go. Implement the account. Repository interface using the sqlc-generated db. Queries object.
2.4. CREATE FILE: internal/account/service.go.
* Define a Repository interface (Create, FindByEmail, etc.).
* Define an AuthService struct containing the Repository, a *redis. Client, and the jwtKey string.
* Implement a RegisterUser method.
* Implement a Login method. This method MUST:
a. Generate a short-lived JWT Access Token with a jti claim.
b. Generate a secure random string for the Refresh Token.
c. Store the Refresh Token in Redis with a long TTL (30 days). Key: refresh_token:<token>. Value: user_id.
d. Store the Access Token's jti in Redis with a short TTL (15 minutes). Key: session:<jti>. Value: user_id.
e. Return both tokens.
* Implement a RefreshToken method. This method MUST:
a. Find the provided refresh token in Redis.
b. If found, delete the old refresh token key.
c. Execute the same token generation and Redis storage logic as the Login method (token rotation).
d. Return the new token pair.

TASK 3: API AND MIDDLEWARE LAYER

3.1. CREATE FILE: internal/platform/server/handlers/auth_handler.go.
* Create an AuthHandler struct containing the *account. AuthService.
* Implement handlers for Register(c *gin. Context), Login(c *gin. Context), and Refresh(c *gin. Context).

3.2. CREATE FILE: internal/platform/server/middleware/auth.go.
* Create a Gin middleware function Authenticator(jwtKey string, redisClient *redis. Client) gin. HandlerFunc.
* This middleware MUST extract the JWT from the header, validate it, and then check if the jti claim exists as a key in Redis.
* If the jti key does not exist in Redis, it MUST respond with a 401 Unauthorized error.
* On success, it MUST add userID, companyID, and roles from the token claims to the Gin context.

3.3. MODIFY FILE: cmd/server/main.go.
* Instantiate the Redis client.
* Instantiate the PostgresAccountRepository and AuthService, passing the Redis client to the service.
* Instantiate the AuthHandler.
* Create a public /auth route group for /login, /register, and /refresh.
* Create a protected /v1 route group and apply the middleware. Authenticator.
* Add a sample protected route GET /v1/me.

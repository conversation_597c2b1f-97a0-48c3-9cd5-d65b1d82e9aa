1.  **A detailed guide for a human developer team**, with explanations and best practices.
2.  **A structured task list for an AI coding agent**, with imperative and specific instructions.

-----

### **Plan for Human Developer Team: Phase 1 (Refined)**

**Goal:** To establish a production-grade project skeleton for the new Payment Service, incorporating best-practice tools for database access, configuration, logging, error handling, and API responses.

#### **Task 1: Project Scaffolding and Tooling**

1.  **Initialize Project:**

      * Create the root directory: `mkdir payment-service-v2 && cd payment-service-v2`
      * Initialize the Go module: `go mod init github.com/your-org/payment-service-v2` (replace with your actual module path).
      * Create the directory structure as previously defined (`/cmd`, `/internal`, `/configs`, `/migrations`).

2.  **Install Command-Line Tools:**

      * **`golang-migrate`:** This tool will manage our SQL schema versions. It's crucial for maintaining a consistent database state across all environments.
        ```bash
        # Follow installation instructions at https://github.com/golang-migrate/migrate
        # Example for macOS: brew install golang-migrate
        ```
      * **`sqlc`:** This tool generates type-safe Go code from our SQL queries, eliminating the need for a heavy ORM and preventing a whole class of runtime errors.
        ```bash
        # Follow installation instructions at https://docs.sqlc.dev/en/latest/overview/install.html
        # Example: go install github.com/sqlc-dev/sqlc/cmd/sqlc@latest
        ```

#### **Task 2: Core Infrastructure Implementation**

This is where we build the "platform" of our application.

1.  **Configuration (`koanf`):**

      * **Action:** Create the `configs/config.yaml` file and the `internal/platform/config/config.go` package as detailed in the previous response. This setup allows for easy management of settings across different environments.
      * **Best Practice:** Ensure you also create a `configs/config.example.yaml` file and commit it to version control, while the actual `config.yaml` is git-ignored.

2.  **Structured Logging (`zerolog`):**

      * **Action:** Implement the `internal/platform/logger/logger.go` package. The key here is to provide different outputs for development and production.
      * **Code Snippet (`internal/platform/logger/logger.go`):**
        ```go
        package logger

        import (
            "os"
            "strings"
            "time"
            "github.com/rs/zerolog"
            "github.com/rs/zerolog/log"
        )

        func Init(level, mode string) {
            logLevel, err := zerolog.ParseLevel(strings.ToLower(level))
            if err != nil {
                logLevel = zerolog.InfoLevel
            }
            zerolog.SetGlobalLevel(logLevel)
            
            // Use beautiful, human-readable console output for development
            if mode == "development" {
                log.Logger = log.Output(zerolog.ConsoleWriter{
                    Out:        os.Stderr,
                    TimeFormat: time.RFC3339,
                })
            } else {
            // Use structured JSON for production for easy parsing by log aggregators
                log.Logger = zerolog.New(os.Stderr).With().Timestamp().Logger()
            }
        }
        ```

3.  **Database Setup (`golang-migrate` & `sqlc`):**

      * **Action (Migration):** Create your first migration.

        ```bash
        migrate create -ext sql -dir migrations -seq create_initial_tables
        ```

        In the `.up.sql` file, add your initial `accounts` and `ledger_transactions` table definitions using `BIGINT` for money and `UUID` for keys.

      * **Action (`sqlc`):** Create the `sqlc.yaml` file in the project root. Then, create your first query file.

        **File: `internal/persistence/queries/account.sql`**

        ```sql
        -- name: GetAccount :one
        SELECT * FROM accounts
        WHERE id = $1 AND deleted_at IS NULL
        LIMIT 1;

        -- name: CreateAccount :one
        INSERT INTO accounts (name, email, type, balance, currency_code, is_active)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING *;
        ```

      * **Action (Generate Code):** Run `sqlc generate` from the project root. This will create the `internal/persistence/db` package containing your type-safe database access code.

4.  **Standardized API Responses and Error Handling:**

      * **Action:** Create a new package `internal/platform/webapi`. This will contain our standardized response and error handling logic.
      * **Code Snippet (`internal/platform/webapi/response.go`):**
        ```go
        package webapi

        import (
            "github.com/gin-gonic/gin"
        )

        type successResponse struct {
            Status string      `json:"status"`
            Data   interface{} `json:"data,omitempty"`
        }

        type paginatedResponse struct {
            successResponse
            Pagination interface{} `json:"pagination,omitempty"`
        }

        func Success(c *gin.Context, status int, data interface{}) {
            c.JSON(status, successResponse{Status: "success", Data: data})
        }

        // ... other helpers like SuccessPaginated
        ```
      * **Code Snippet (`internal/platform/webapi/error.go`):**
        ```go
        package webapi

        import (
            "github.com/getsentry/sentry-go"
            "github.com/gin-gonic/gin"
            "github.com/rs/zerolog/log"
        )

        type apiError struct {
            Code    string `json:"code"`
            Message string `json:"message"`
        }

        type errorResponse struct {
            Status string   `json:"status"`
            Error  apiError `json:"error"`
        }

        func Error(c *gin.Context, status int, code, message string) {
            errResp := errorResponse{
                Status: "error",
                Error:  apiError{Code: code, Message: message},
            }
            c.JSON(status, errResp)
        }

        func ErrorWithSentry(c *gin.Context, status int, err error) {
            // Capture the original error in Sentry
            sentry.CaptureException(err)
            log.Error().Err(err).Msg("An internal server error occurred")
            // Return a generic message to the user
            Error(c, status, "INTERNAL_SERVER_ERROR", "An unexpected error occurred. Please try again later.")
        }
        ```

#### **Task 5: Server and Local Environment Setup**

1.  **Integrate Sentry:**

      * Install the Sentry SDK: `go get github.com/getsentry/sentry-go`
      * In `cmd/server/main.go`, initialize Sentry early in the `main` function.
        ```go
        // In main.go
        err := sentry.Init(sentry.ClientOptions{
            Dsn: os.Getenv("SENTRY_DSN"),
            TracesSampleRate: 1.0,
            Environment: cfg.Server.Mode,
        })
        if err != nil {
            log.Fatal().Msgf("Sentry initialization failed: %v", err)
        }
        defer sentry.Flush(2 * time.Second)
        ```

2.  **Set up `docker-compose.yml`:**

      * Create the `docker-compose.yml` file as detailed previously. Make sure to include services for your app (`app`), the database (`db`), and Redis (`redis`).
      * Use environment variables within the compose file to pass database credentials and other settings to the containers.

3.  **Wire Everything in `main.go`:**

      * Your `main.go` should now initialize Config, Logger, Sentry, and the Database Pool. It should then set up the Gin router, add a custom error-handling middleware that uses your `webapi.ErrorWithSentry` function, and finally start the server gracefully.

By the end of this phase, you will have a fully functional, containerized "hello world" application built on a professional-grade foundation.

-----

### **Task List for AI Coding Agent: Phase 1 (Refined)**

**INSTRUCTION:** Execute the following tasks sequentially. Replace `github.com/your-org/payment-service-v2` with the user-provided Go module path.

**TASK 1: SETUP PROJECT STRUCTURE**
1.1. `RUN`: `mkdir -p payment-service-v2/cmd/server payment-service-v2/internal/platform/config payment-service-v2/internal/platform/logger payment-service-v2/internal/platform/database payment-service-v2/internal/platform/webapi payment-service-v2/internal/persistence/queries payment-service-v2/internal/persistence/db payment-service-v2/migrations payment-service-v2/configs`
1.2. `RUN`: `cd payment-service-v2 && go mod init github.com/your-org/payment-service-v2`
1.3. `CREATE FILE`: `.gitignore` with standard Go, IDE, and environment file exclusions.

**TASK 2: IMPLEMENT CORE PLATFORM PACKAGES**
2.1. `INSTALL GO PACKAGES`: `github.com/knadh/koanf/v2`, `github.com/knadh/koanf/parsers/yaml`, `github.com/knadh/koanf/providers/file`, `github.com/knadh/koanf/providers/env`, `github.com/rs/zerolog`, `github.com/jackc/pgx/v5/pgxpool`, `github.com/gin-gonic/gin`, `github.com/getsentry/sentry-go`
2.2. `CREATE FILE`: `configs/config.yaml` with `server`, `logger`, `database`, and `redis` sections.
2.3. `CREATE FILE`: `internal/platform/config/config.go` with the `Config` struct and a `New()` function to load configuration from `configs/config.yaml` and environment variables.
2.4. `CREATE FILE`: `internal/platform/logger/logger.go` with an `Init(level, mode string)` function that configures `zerolog` for console pretty printing in "development" mode and structured JSON logging otherwise.
2.5. `CREATE FILE`: `internal/platform/database/database.go` with a `NewConnection(cfg *config.Config)` function that returns a `*pgxpool.Pool`.
2.6. `CREATE FILE`: `internal/platform/webapi/response.go` with `Success` and `SuccessPaginated` response helper functions for Gin.
2.7. `CREATE FILE`: `internal/platform/webapi/error.go` with `Error` and `ErrorWithSentry` helper functions for Gin.

**TASK 3: SETUP DATABASE MIGRATION AND CODE GENERATION**
3.1. `CREATE FILE`: `sqlc.yaml` in the project root, configured to read schema from `./migrations/` and queries from `./internal/persistence/queries/`, and to generate code to `./internal/persistence/db`. Ensure `emit_interface` is set to `true`.
3.2. `RUN COMMAND (SIMULATED)`: `migrate create -ext sql -dir migrations -seq create_initial_tables`
3.3. `CREATE FILE`: `migrations/000001_create_initial_tables.up.sql` containing `CREATE EXTENSION IF NOT EXISTS "uuid-ossp";` and the schemas for `accounts`, `payment_methods`, `ledger_transactions`, and `ledger_entries` using `UUID` for primary keys and `BIGINT` for monetary values.
3.4. `CREATE FILE`: `migrations/000001_create_initial_tables.down.sql` with the corresponding `DROP TABLE` statements.
3.5. `CREATE FILE`: `internal/persistence/queries/placeholder.sql` with a simple query like `-- name: Placeholder :one\nSELECT 1;` to allow the first `sqlc generate` run to succeed.

**TASK 4: SETUP LOCAL DEVELOPMENT ENVIRONMENT**
4.1. `CREATE FILE`: `Dockerfile` using a multi-stage build to create a minimal final image.
4.2. `CREATE FILE`: `docker-compose.yml` defining three services: `app`, `db` (using `postgres:15-alpine`), and `redis` (using `redis:7-alpine`). Configure necessary environment variables and volumes.

**TASK 5: CREATE MAIN APPLICATION ENTRYPOINT**
5.1. `CREATE FILE`: `cmd/server/main.go`.
5.2. **Implement `main()` function to:**
a. Initialize config using the `config` package.
b. Initialize logger using the `logger` package.
c. Initialize Sentry using the DSN from an environment variable (`SENTRY_DSN`).
d. Initialize the database connection pool.
e. Initialize a new Gin router.
f. Add `gin.Recovery()` middleware.
g. Create a `/health` endpoint that pings the database.
h. Implement graceful server shutdown on `SIGINT` or `SIGTERM`.
i. Start the HTTP server.

**END OF PHASE 1**
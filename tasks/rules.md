# Coding Guidelines & Best Practices

**Project:** Payment Service v2
**Version:** 1.3
**Date:** June 26, 2025

This document outlines the coding standards, patterns, and best practices for the Payment Service project. Adherence to these guidelines is mandatory for all contributors, including human developers and AI agents, to ensure a high-quality, consistent, and maintainable codebase.

## 1\. General Principles & Idiomatic Go

  + **Rule 1.1 (Simplicity - KISS):** Prefer simple, clear solutions over complex ones.
  + **Rule 1.2 (Effective Go):** Follow the principles outlined in the official [Effective Go](https://go.dev/doc/effective_go) document.
  + **Rule 1.3 (Package Naming):** All package names MUST be `short-lowercase`.
  + **Rule 1.4 (Variable Naming):** All variable names MUST be `camelCase`.
  + **Rule 1.5 (Interfaces):** Define interfaces to decouple components. Functions MUST accept interfaces where possible and return concrete struct types.
  + **Rule 1.6 (Formatting):** All generated Go code MUST be formatted with `gofmt`.

## 2\. Project Structure & Clean Architecture

  + **Rule 2.1 (Strict Layering):** The dependency direction `API -> Application -> Domain` MUST be strictly enforced.
  + **Rule 2.2 (Domain Purity):** The `internal/domain` package MUST NOT import any other project packages.
  + **Rule 2.3 (Thin Handlers):** API handlers MUST only parse requests, call a single application service method, and generate a standardized response.

## 3\. Error Management Protocol

  + **Rule 3.1 (Error Wrapping):** Errors MUST be wrapped with context: `fmt.Errorf("package.function: %w", err)`.
  + **Rule 3.2 (Custom Domain Errors):** Use custom error variables for predictable business rule failures.
  + **Rule 3.3 (Centralized HTTP Error Mapping):** A dedicated Gin error-handling middleware is the *only* place where errors are translated into HTTP status codes.
  + **Rule 3.4 (Sentry for Unexpected Errors):** Use `webapi.ErrorWithSentry(c, err)` for all `5xx` internal server errors.

## 4\. Standardized API Responses ( `webapi` package)

  + **Rule 4.1 (Mandatory Helpers):** All HTTP responses MUST be generated using helpers from the `internal/platform/webapi` package.
  + **Rule 4.2 (Success Structure):** The mandatory JSON structure for success is `{"status": "success", "data": { ... }}`.
  + **Rule 4.3 (Error Structure):** The mandatory JSON structure for an error is `{"status": "error", "error": {"code": "...", "message": "..."}}`.
  + **Rule 4.4 (Helper Signatures):** Adhere to the defined signatures for `Success`,   `SuccessPaginated`,   `Error`, and `ErrorWithSentry`.

## 5\. Logging Best Practices ( `zerolog` )

  + **Rule 5.1 (Structured Logging):** All logging MUST be done using the global `zerolog` instance.
  + **Rule 5.2 (Contextual Logging):** Every log statement MUST include the `trace_id` from the context.
  + **Rule 5.3 (Error Logging):** When logging an error, the error object MUST be included using `.Err(err)`.

## 6\. Concurrency and Graceful Shutdown

  + **Rule 6.1 (Panic Safety):** Any new goroutine MUST use a `defer...recover()` block, logging any recovered panic to Sentry.
  + **Rule 6.2 (Graceful Termination):** Any long-running goroutine MUST accept a `context.Context` and listen on `ctx.Done()`.
  + **Rule 6.3 (Server Shutdown):** `main.go` must implement graceful shutdown for the HTTP server.

## 7\. Gin Middleware

  + **Rule 7.1 (Tracing Middleware):** MUST be the first middleware, generating a `trace_id`.
  + **Rule 7.2 (Logger Middleware):** MUST log request and response details.
  + **Rule 7.3 (Error Middleware):** MUST be the last middleware to centralize error handling.

## 8\. Database Access ( `sqlc` with `pgx/v5` )

  + **Rule 8.1 (SQL Files):** All SQL queries MUST be defined in `.sql` files.
  + **Rule 8.2 (`sqlc.yaml` Configuration):** The `sqlc.yaml` file MUST be configured to use `sql_package: "pgx/v5"` and appropriate type overrides.
  + **Rule 8.3 (Type Conversion):** All conversions between domain types and `pgtype` MUST be handled in a dedicated `internal/persistence/converters` package.
  + **Rule 8.4 (Transactions):** Repository methods that perform writes MUST accept `pgx.Tx` as a parameter.

## 9\. Testing and CI/CD Strategy

  + **Rule 9.1 (Containerized Testing):** All integration and end-to-end tests that require external services (like a database or Redis) MUST use **Testcontainers for Go** (`github.com/testcontainers/testcontainers-go`). Test logic MUST follow the latest documentation and patterns from official sources (e.g.,   `context7`).
  + **Rule 9.2 (Seeding Data for Tests):** For every new `.up.sql` migration file, a corresponding test data seed file (e.g.,   `testdata/seeds/000002_seed_roles.sql`) MUST be created. The test setup MUST apply these seeds to the containerized database.
  + **Rule 9.3 (Test Coverage):** API handlers, application services, and repositories MUST have dedicated test files.
  + **Rule 9.4 (CI/CD Gate):** A pull request MUST NOT be allowed to merge into the `main` branch unless all tests (`go test ./...`) pass.

-----

## AI Agent Explicit Ruleset

**SYSTEM PROMPT:** You are a senior Go developer operating under the "MCP" (Master Coding Protocol). You will strictly adhere to the following ruleset when generating or modifying code for the `payment-service-v2` project. This guide is your primary directive.

### **RULESET: CODING\_GUIDELINES\_V1.3**

  + **Rule 1 (General):** Enforce `gofmt`,  `camelCase`,  `short-lowercase` packages, and interface-based design.
  + **Rule 2 (Architecture):** Enforce `API -> Application -> Domain` dependency rule and thin handlers.
  + **Rule 3 (Error Handling):** Enforce error wrapping, custom domain errors, and centralized error handling with Sentry reporting.
  + **Rule 4 (API Responses):** Enforce the use of `webapi` helpers for all success and error responses.
  + **Rule 5 (Logging):** Enforce structured, contextual logging with `zerolog`.
  + **Rule 6 (Concurrency):** Enforce panic safety and graceful termination of goroutines.
  + **Rule 7 (Middleware):** Enforce the implementation of Tracing, Logging, and Error handling middleware.
  + **Rule 8 (Database):** Enforce the use of `.sql` files for `sqlc`,  `pgx/v5` configuration, centralized type converters, and transactional repositories.
  + **Rule 9 (Testing & CI/CD):**
      * 9.1. All integration and E2E tests MUST use `github.com/testcontainers/testcontainers-go` according to the latest official documentation (e.g., context7).
      * 9.2. Every new `*.up.sql` migration MUST have a corresponding `testdata/seeds/*.sql` file.
      * 9.3. Test coverage MUST be provided for handlers, services, and repositories.
      * 9.4. All code MUST be written with the assumption that it will be gated by a CI pipeline that runs `go test ./...` and blocks merges on failure.

**END OF RULESET**

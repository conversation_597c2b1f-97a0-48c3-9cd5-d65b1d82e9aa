Phase 2.1 Development Guide: Testing & Validation
Goal: To achieve comprehensive test coverage for the authentication and user management module, ensuring its correctness, security, and adherence to the API contract. This phase validates the work done in Phase 2.

Task 1: Test Infrastructure and Seeding
This task prepares the environment for our containerized testing strategy.

Implement Testcontainer Setup Helpers:

File: Create a new test helper package, e.g., internal/platform/testhelpers/.

Action: Implement a function SetupTestDatabase(ctx context.Context) (*pgxpool.Pool, func()). This function will:

Use testcontainers-go to programmatically start a PostgreSQL container.

Retrieve the container's dynamic connection string.

Run all migrations from the /migrations directory against the test database.

Return the connection pool (*pgxpool.Pool) and a cleanup function that terminates the container.

Best Practice: The test setup should run once per test suite (TestMain) to improve performance, rather than for every single test.

Create Test Data Seed File:

Action: Following Rule 9.2, create a seed file corresponding to our auth migration.

File: testdata/seeds/000002_seed_auth_data.sql

SQL: Add INSERT statements to populate the roles, permissions, and role_permissions tables with initial data (e.g., "company_admin" role, "payments:create" permission).

INSERT INTO roles (id, name) VALUES ('...', 'admin');
INSERT INTO permissions (id, name) VALUES ('...', 'users:create');
INSERT INTO role_permissions (role_id, permission_id) VALUES ('...', '...');

Action: Update the SetupTestDatabase helper to execute this seed file after running the migrations.

Task 2: Repository Integration Tests
Goal: To verify that the data access layer (sqlc-generated code and its repository wrapper) correctly interacts with a real PostgreSQL database.

Create the Test File:

File: internal/persistence/postgres/account_repository_test.go

Implement Tests:

Action: For each public method in account_repository.go (e.g., Create, FindByEmail, AssignRoleToAccount), write a corresponding test function.

Test Logic (TestCreateAccount):

Start the test database using the SetupTestDatabase helper.

Create a new domain.Account object.

Call the repo.Create() method.

Assert that the returned error is nil.

Query the test database directly to verify that the account was inserted with the correct data.

Test Logic (TestFindByEmail):

Use the seeded data or create a new account.

Call repo.FindByEmail().

Assert that the returned account matches the expected data and the error is nil.

Test the negative case (email not found) and assert that the repository returns a specific domain error (e.g., domain.ErrNotFound).

Task 3: Application Service Integration Tests
Goal: To test the business logic of the AuthService in isolation from the HTTP layer but integrated with the data layer.

Create the Test File:

File: internal/account/service_test.go

Implement Tests:

Action: Write test functions for RegisterUser, Login, and RefreshToken.

Test Logic (TestRegisterUser):

Start the test database and a Testcontainers Redis instance.

Instantiate the real PostgresAccountRepository.

Instantiate the real AuthService, injecting the repository and Redis client.

Call authService.RegisterUser() with valid data.

Assert that the error is nil.

Verify that the user was created in the database.

Test failure cases, such as registering with a duplicate email, and assert that the correct domain error is returned.

Test Logic (TestLoginAndRefresh):

Create a user via the service.

Call authService.Login() with correct credentials. Assert that an access token and refresh token are returned.

Call authService.Login() with incorrect credentials. Assert that an error is returned.

Call authService.RefreshToken() with the valid refresh token. Assert that a new token pair is returned.

Verify in Redis that the old refresh token was deleted and the new one was stored.

Call authService.RefreshToken() with an invalid or expired token. Assert that an error is returned.

Task 4: API Handler End-to-End Tests
Goal: To verify the full HTTP flow, from request parsing to the final JSON response, ensuring all components work together correctly.

Create the Test File:

File: internal/platform/server/handlers/auth_handler_test.go

Implement E2E Tests:

Action: In your test setup, start the full application stack (Postgres, Redis) using Testcontainers. Then, create an instance of your Gin router.

Test Logic (TestRegisterAndLoginEndpoints):

Use Go's net/http/httptest package to create a mock HTTP request (POST /auth/register).

Create a httptest.ResponseRecorder to capture the response.

Serve the request through your Gin router: router.ServeHTTP(recorder, request).

Assert that the HTTP status code is 201 Created.

Now, create a new request for POST /auth/login with the credentials you just registered.

Serve the request. Assert the status code is 200 OK and that the JSON response body contains access_token and refresh_token fields.

Test Logic (TestProtectedEndpoint):

Perform a login to get a valid access token.

Create a request for a protected endpoint (e.g., GET /v1/me).

Set the Authorization header correctly: req.Header.Set("Authorization", "Bearer " + accessToken).

Serve the request. Assert the status code is 200 OK.

Create another request to the same endpoint without the Authorization header. Assert the status code is 401 Unauthorized.
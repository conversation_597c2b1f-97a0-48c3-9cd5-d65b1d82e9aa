package account

import (
	"context"
	"crypto/rand"
	"encoding/base64"
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/redis/go-redis/v9"

	platformredis "github.com/wongpinter/payment/internal/platform/redis"
)

// Repository defines the interface for account data access
// Rule 1.4: Function parameters accept interfaces where possible
type Repository interface {
	CreateUser(ctx context.Context, tx pgx.Tx, acc *Account) (*Account, error)
	CreateCompany(ctx context.Context, tx pgx.Tx, acc *Account) (*Account, error)
	GetByID(ctx context.Context, id uuid.UUID) (*Account, error)
	GetByEmail(ctx context.Context, email string) (*Account, error)
	UpdatePassword(ctx context.Context, tx pgx.Tx, id uuid.UUID, passwordHash string) error
	Update(ctx context.Context, tx pgx.Tx, acc *Account) (*Account, error)
	SoftDelete(ctx context.Context, tx pgx.Tx, id uuid.UUID) error
	AssignRole(ctx context.Context, tx pgx.Tx, accountID, roleID, companyID uuid.UUID) error
	RemoveRole(ctx context.Context, tx pgx.Tx, accountID, roleID, companyID uuid.UUID) error
	GetRolesForCompany(ctx context.Context, accountID, companyID uuid.UUID) ([]*Role, error)
	GetPermissionsForCompany(ctx context.Context, accountID, companyID uuid.UUID) ([]string, error)
}

// AuthService provides authentication and user management functionality
type AuthService struct {
	repo           Repository
	sessionManager *platformredis.SessionManager
	jwtSecret      string
	accessTTL      time.Duration
	refreshTTL     time.Duration
	issuer         string
}

// NewAuthService creates a new authentication service
func NewAuthService(repo Repository, redisClient *redis.Client, jwtSecret string, accessTTL, refreshTTL time.Duration, issuer string) *AuthService {
	return &AuthService{
		repo:           repo,
		sessionManager: platformredis.NewSessionManager(redisClient),
		jwtSecret:      jwtSecret,
		accessTTL:      accessTTL,
		refreshTTL:     refreshTTL,
		issuer:         issuer,
	}
}

// RegisterUserRequest represents a user registration request
type RegisterUserRequest struct {
	Name     string `json:"name" binding:"required,min=2,max=100"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=8"`
}

// LoginRequest represents a login request
type LoginRequest struct {
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required"`
}

// TokenResponse represents the response containing access and refresh tokens
type TokenResponse struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"`
	TokenType    string `json:"token_type"`
}

// JWTClaims represents the custom JWT claims
type JWTClaims struct {
	UserID    string   `json:"user_id"`
	CompanyID string   `json:"company_id,omitempty"`
	Roles     []string `json:"roles,omitempty"`
	jwt.RegisteredClaims
}

// RegisterUser registers a new user account
// Rule 3.1: Errors wrapped with context
func (s *AuthService) RegisterUser(ctx context.Context, req RegisterUserRequest) (*Account, error) {
	// Check if user already exists
	existingUser, err := s.repo.GetByEmail(ctx, req.Email)
	if err == nil && existingUser != nil {
		return nil, fmt.Errorf("authService.RegisterUser: %w", ErrAccountExists)
	}
	if err != nil && !errors.Is(err, ErrAccountNotFound) {
		return nil, fmt.Errorf("authService.RegisterUser: failed to check existing user: %w", err)
	}

	// Create new user account
	newUser := NewUserAccount(req.Name, req.Email)
	if err := newUser.SetPassword(req.Password); err != nil {
		return nil, fmt.Errorf("authService.RegisterUser: %w", err)
	}

	// Save to database
	createdUser, err := s.repo.CreateUser(ctx, nil, newUser)
	if err != nil {
		return nil, fmt.Errorf("authService.RegisterUser: %w", err)
	}

	return createdUser, nil
}

// Login authenticates a user and returns JWT tokens
func (s *AuthService) Login(ctx context.Context, req LoginRequest) (*TokenResponse, error) {
	// Get user by email
	user, err := s.repo.GetByEmail(ctx, req.Email)
	if err != nil {
		if errors.Is(err, ErrAccountNotFound) {
			return nil, fmt.Errorf("authService.Login: %w", ErrInvalidCredentials)
		}
		return nil, fmt.Errorf("authService.Login: %w", err)
	}

	// Check if user can login
	if !user.CanLogin() {
		return nil, fmt.Errorf("authService.Login: %w", ErrInvalidCredentials)
	}

	// Verify password
	if !user.CheckPassword(req.Password) {
		return nil, fmt.Errorf("authService.Login: %w", ErrInvalidCredentials)
	}

	// Generate tokens
	tokenResponse, err := s.generateTokenPair(ctx, user)
	if err != nil {
		return nil, fmt.Errorf("authService.Login: %w", err)
	}

	return tokenResponse, nil
}

// RefreshToken generates new tokens using a refresh token
func (s *AuthService) RefreshToken(ctx context.Context, refreshToken string) (*TokenResponse, error) {
	// Validate refresh token and get user ID
	userID, err := s.sessionManager.GetRefreshToken(ctx, refreshToken)
	if err != nil {
		return nil, fmt.Errorf("authService.RefreshToken: invalid refresh token: %w", err)
	}

	// Get user by ID
	userUUID, err := uuid.Parse(userID)
	if err != nil {
		return nil, fmt.Errorf("authService.RefreshToken: invalid user ID: %w", err)
	}

	user, err := s.repo.GetByID(ctx, userUUID)
	if err != nil {
		return nil, fmt.Errorf("authService.RefreshToken: %w", err)
	}

	// Check if user can still login
	if !user.CanLogin() {
		return nil, fmt.Errorf("authService.RefreshToken: %w", ErrInvalidCredentials)
	}

	// Delete old refresh token (token rotation)
	if err := s.sessionManager.DeleteRefreshToken(ctx, refreshToken); err != nil {
		return nil, fmt.Errorf("authService.RefreshToken: failed to delete old token: %w", err)
	}

	// Generate new token pair
	tokenResponse, err := s.generateTokenPair(ctx, user)
	if err != nil {
		return nil, fmt.Errorf("authService.RefreshToken: %w", err)
	}

	return tokenResponse, nil
}

// generateTokenPair creates access and refresh tokens for a user
func (s *AuthService) generateTokenPair(ctx context.Context, user *Account) (*TokenResponse, error) {
	// Generate JTI for access token
	jti := uuid.New().String()

	// Create JWT claims
	now := time.Now().UTC()
	claims := JWTClaims{
		UserID: user.ID.String(),
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        jti,
			Subject:   user.ID.String(),
			Issuer:    s.issuer,
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(s.accessTTL)),
		},
	}

	// Create and sign JWT
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	accessToken, err := token.SignedString([]byte(s.jwtSecret))
	if err != nil {
		return nil, fmt.Errorf("authService.generateTokenPair: failed to sign JWT: %w", err)
	}

	// Generate refresh token
	refreshToken, err := s.generateSecureToken()
	if err != nil {
		return nil, fmt.Errorf("authService.generateTokenPair: failed to generate refresh token: %w", err)
	}

	// Store tokens in Redis
	userIDStr := user.ID.String()

	// Store refresh token
	if err := s.sessionManager.StoreRefreshToken(ctx, refreshToken, userIDStr, s.refreshTTL); err != nil {
		return nil, fmt.Errorf("authService.generateTokenPair: failed to store refresh token: %w", err)
	}

	// Store access token JTI
	if err := s.sessionManager.StoreAccessTokenJTI(ctx, jti, userIDStr, s.accessTTL); err != nil {
		return nil, fmt.Errorf("authService.generateTokenPair: failed to store access token JTI: %w", err)
	}

	return &TokenResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresIn:    int64(s.accessTTL.Seconds()),
		TokenType:    "Bearer",
	}, nil
}

// generateSecureToken generates a cryptographically secure random token
func (s *AuthService) generateSecureToken() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("authService.generateSecureToken: %w", err)
	}
	return base64.URLEncoding.EncodeToString(bytes), nil
}

// ValidateAccessToken validates an access token and returns the claims
func (s *AuthService) ValidateAccessToken(ctx context.Context, tokenString string) (*JWTClaims, error) {
	// Parse and validate JWT
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(s.jwtSecret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("authService.ValidateAccessToken: invalid token: %w", err)
	}

	claims, ok := token.Claims.(*JWTClaims)
	if !ok || !token.Valid {
		return nil, fmt.Errorf("authService.ValidateAccessToken: invalid token claims")
	}

	// Check if token exists in Redis (session validation)
	_, err = s.sessionManager.ValidateAccessTokenJTI(ctx, claims.ID)
	if err != nil {
		return nil, fmt.Errorf("authService.ValidateAccessToken: session not found: %w", err)
	}

	return claims, nil
}

// Logout invalidates all tokens for a user
func (s *AuthService) Logout(ctx context.Context, userID uuid.UUID) error {
	if err := s.sessionManager.RevokeAllUserSessions(ctx, userID.String()); err != nil {
		return fmt.Errorf("authService.Logout: %w", err)
	}
	return nil
}

package account

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestAccount_SetPassword tests the SetPassword method
// Following Rule 9.3: Test coverage for domain models
func TestAccount_SetPassword(t *testing.T) {
	account := NewUserAccount("<PERSON>", "<EMAIL>")

	// Test successful password setting
	t.Run("successful password setting", func(t *testing.T) {
		err := account.SetPassword("securepassword123")
		require.NoError(t, err, "SetPassword should not return error for valid password")
		assert.NotEmpty(t, account.PasswordHash, "Password hash should be set")
	})

	// Test weak password error
	t.Run("weak password error", func(t *testing.T) {
		err := account.SetPassword("short")
		assert.Error(t, err, "SetPassword should return error for weak password")
		assert.ErrorIs(t, err, ErrWeakPassword, "Should return specific domain error")
	})
}

// TestAccount_CheckPassword tests the CheckPassword method
// Following Rule 9.3: Test coverage for domain models
func TestAccount_CheckPassword(t *testing.T) {
	account := NewUserAccount("<PERSON> Doe", "<EMAIL>")
	password := "securepassword123"

	// Set password first
	err := account.SetPassword(password)
	require.NoError(t, err, "Failed to set password")

	// Test correct password
	t.Run("correct password", func(t *testing.T) {
		isValid := account.CheckPassword(password)
		assert.True(t, isValid, "CheckPassword should return true for correct password")
	})

	// Test incorrect password
	t.Run("incorrect password", func(t *testing.T) {
		isValid := account.CheckPassword("wrongpassword")
		assert.False(t, isValid, "CheckPassword should return false for incorrect password")
	})

	// Test empty password hash
	t.Run("empty password hash", func(t *testing.T) {
		emptyAccount := NewUserAccount("Jane Doe", "<EMAIL>")
		isValid := emptyAccount.CheckPassword("anypassword")
		assert.False(t, isValid, "CheckPassword should return false for empty password hash")
	})
}

// TestAccount_IsUser tests the IsUser method
// Following Rule 9.3: Test coverage for domain models
func TestAccount_IsUser(t *testing.T) {
	userAccount := NewUserAccount("John Doe", "<EMAIL>")
	companyAccount := NewCompanyAccount("Acme Corp", "<EMAIL>")

	assert.True(t, userAccount.IsUser(), "User account should return true for IsUser")
	assert.False(t, companyAccount.IsUser(), "Company account should return false for IsUser")
}

// TestAccount_IsCompany tests the IsCompany method
// Following Rule 9.3: Test coverage for domain models
func TestAccount_IsCompany(t *testing.T) {
	userAccount := NewUserAccount("John Doe", "<EMAIL>")
	companyAccount := NewCompanyAccount("Acme Corp", "<EMAIL>")

	assert.False(t, userAccount.IsCompany(), "User account should return false for IsCompany")
	assert.True(t, companyAccount.IsCompany(), "Company account should return true for IsCompany")
}

// TestAccount_CanLogin tests the CanLogin method
// Following Rule 9.3: Test coverage for domain models
func TestAccount_CanLogin(t *testing.T) {
	// Test active user account
	t.Run("active user can login", func(t *testing.T) {
		userAccount := NewUserAccount("John Doe", "<EMAIL>")
		assert.True(t, userAccount.CanLogin(), "Active user account should be able to login")
	})

	// Test inactive user account
	t.Run("inactive user cannot login", func(t *testing.T) {
		userAccount := NewUserAccount("John Doe", "<EMAIL>")
		userAccount.IsActive = false
		assert.False(t, userAccount.CanLogin(), "Inactive user account should not be able to login")
	})

	// Test company account
	t.Run("company account cannot login", func(t *testing.T) {
		companyAccount := NewCompanyAccount("Acme Corp", "<EMAIL>")
		assert.False(t, companyAccount.CanLogin(), "Company account should not be able to login")
	})
}

// TestNewUserAccount tests the NewUserAccount constructor
// Following Rule 9.3: Test coverage for domain models
func TestNewUserAccount(t *testing.T) {
	name := "John Doe"
	email := "<EMAIL>"

	account := NewUserAccount(name, email)

	assert.Equal(t, name, account.Name, "Name should match")
	assert.Equal(t, email, account.Email, "Email should match")
	assert.Equal(t, AccountTypePersonal, account.Type, "Type should be personal")
	assert.Equal(t, int64(0), account.Balance, "Balance should be 0")
	assert.Equal(t, "USD", account.CurrencyCode, "Currency should be USD")
	assert.True(t, account.IsActive, "Account should be active")
	assert.NotZero(t, account.ID, "ID should be generated")
	assert.NotZero(t, account.CreatedAt, "CreatedAt should be set")
	assert.NotZero(t, account.UpdatedAt, "UpdatedAt should be set")
}

// TestNewCompanyAccount tests the NewCompanyAccount constructor
// Following Rule 9.3: Test coverage for domain models
func TestNewCompanyAccount(t *testing.T) {
	name := "Acme Corp"
	email := "<EMAIL>"

	account := NewCompanyAccount(name, email)

	assert.Equal(t, name, account.Name, "Name should match")
	assert.Equal(t, email, account.Email, "Email should match")
	assert.Equal(t, AccountTypeBusiness, account.Type, "Type should be business")
	assert.Equal(t, int64(0), account.Balance, "Balance should be 0")
	assert.Equal(t, "USD", account.CurrencyCode, "Currency should be USD")
	assert.True(t, account.IsActive, "Account should be active")
	assert.NotZero(t, account.ID, "ID should be generated")
	assert.NotZero(t, account.CreatedAt, "CreatedAt should be set")
	assert.NotZero(t, account.UpdatedAt, "UpdatedAt should be set")
}

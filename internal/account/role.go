package account

import (
	"time"

	"github.com/google/uuid"
)

// Role represents a role in the RBAC system
type Role struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Permission represents a permission in the RBAC system
type Permission struct {
	ID          uuid.UUID `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description,omitempty"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// AccountRole represents the assignment of a role to an account within a company context
type AccountRole struct {
	AccountID uuid.UUID `json:"account_id"`
	RoleID    uuid.UUID `json:"role_id"`
	CompanyID uuid.UUID `json:"company_id"`
	CreatedAt time.Time `json:"created_at"`
}

// Predefined role names
const (
	RoleAdmin    = "admin"
	RoleManager  = "manager"
	RoleEmployee = "employee"
	RoleViewer   = "viewer"
	RoleOwner    = "owner"
	RoleFinance  = "finance"
	RoleSupport  = "support"
)

// Predefined permission names
const (
	PermissionCreatePayment     = "payment:create"
	PermissionReadPayment       = "payment:read"
	PermissionUpdatePayment     = "payment:update"
	PermissionDeletePayment     = "payment:delete"
	PermissionCreateAccount     = "account:create"
	PermissionReadAccount       = "account:read"
	PermissionUpdateAccount     = "account:update"
	PermissionDeleteAccount     = "account:delete"
	PermissionManageRoles       = "role:manage"
	PermissionViewReports       = "report:view"
	PermissionManageCompany     = "company:manage"
	PermissionViewTransactions  = "transaction:view"
	PermissionCreateTransaction = "transaction:create"
)

// NewRole creates a new role
// Rule 1.2: camelCase variable names
func NewRole(name, description string) *Role {
	return &Role{
		ID:          uuid.New(),
		Name:        name,
		Description: description,
		CreatedAt:   time.Now().UTC(),
		UpdatedAt:   time.Now().UTC(),
	}
}

// NewPermission creates a new permission
func NewPermission(name, description string) *Permission {
	return &Permission{
		ID:          uuid.New(),
		Name:        name,
		Description: description,
		CreatedAt:   time.Now().UTC(),
		UpdatedAt:   time.Now().UTC(),
	}
}

// NewAccountRole creates a new account role assignment
func NewAccountRole(accountID, roleID, companyID uuid.UUID) *AccountRole {
	return &AccountRole{
		AccountID: accountID,
		RoleID:    roleID,
		CompanyID: companyID,
		CreatedAt: time.Now().UTC(),
	}
}

// GetDefaultRoles returns the default roles that should be created in the system
func GetDefaultRoles() []*Role {
	return []*Role{
		NewRole(RoleOwner, "Company owner with full access"),
		NewRole(RoleAdmin, "Administrator with full management access"),
		NewRole(RoleManager, "Manager with team management access"),
		NewRole(RoleEmployee, "Employee with standard access"),
		NewRole(RoleViewer, "Read-only access to company data"),
		NewRole(RoleFinance, "Financial operations and reporting access"),
		NewRole(RoleSupport, "Customer support access"),
	}
}

// GetDefaultPermissions returns the default permissions that should be created in the system
func GetDefaultPermissions() []*Permission {
	return []*Permission{
		NewPermission(PermissionCreatePayment, "Create new payments"),
		NewPermission(PermissionReadPayment, "View payment information"),
		NewPermission(PermissionUpdatePayment, "Update payment details"),
		NewPermission(PermissionDeletePayment, "Delete payments"),
		NewPermission(PermissionCreateAccount, "Create new accounts"),
		NewPermission(PermissionReadAccount, "View account information"),
		NewPermission(PermissionUpdateAccount, "Update account details"),
		NewPermission(PermissionDeleteAccount, "Delete accounts"),
		NewPermission(PermissionManageRoles, "Manage roles and permissions"),
		NewPermission(PermissionViewReports, "View financial reports"),
		NewPermission(PermissionManageCompany, "Manage company settings"),
		NewPermission(PermissionViewTransactions, "View transaction history"),
		NewPermission(PermissionCreateTransaction, "Create new transactions"),
	}
}

// GetRolePermissions returns the default permission assignments for roles
func GetRolePermissions() map[string][]string {
	return map[string][]string{
		RoleOwner: {
			PermissionCreatePayment, PermissionReadPayment, PermissionUpdatePayment, PermissionDeletePayment,
			PermissionCreateAccount, PermissionReadAccount, PermissionUpdateAccount, PermissionDeleteAccount,
			PermissionManageRoles, PermissionViewReports, PermissionManageCompany,
			PermissionViewTransactions, PermissionCreateTransaction,
		},
		RoleAdmin: {
			PermissionCreatePayment, PermissionReadPayment, PermissionUpdatePayment,
			PermissionCreateAccount, PermissionReadAccount, PermissionUpdateAccount,
			PermissionManageRoles, PermissionViewReports,
			PermissionViewTransactions, PermissionCreateTransaction,
		},
		RoleManager: {
			PermissionCreatePayment, PermissionReadPayment, PermissionUpdatePayment,
			PermissionReadAccount, PermissionUpdateAccount,
			PermissionViewReports, PermissionViewTransactions, PermissionCreateTransaction,
		},
		RoleEmployee: {
			PermissionCreatePayment, PermissionReadPayment,
			PermissionReadAccount, PermissionViewTransactions,
		},
		RoleViewer: {
			PermissionReadPayment, PermissionReadAccount, PermissionViewTransactions,
		},
		RoleFinance: {
			PermissionCreatePayment, PermissionReadPayment, PermissionUpdatePayment,
			PermissionViewReports, PermissionViewTransactions, PermissionCreateTransaction,
		},
		RoleSupport: {
			PermissionReadPayment, PermissionReadAccount, PermissionViewTransactions,
		},
	}
}

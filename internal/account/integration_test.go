package account_test

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/wongpinter/payment/internal/account"
	"github.com/wongpinter/payment/internal/persistence/postgres"
	"github.com/wongpinter/payment/internal/platform/testhelpers"
)

// TestMain sets up the test environment once for all tests
// Following Rule 9.1: Use testcontainers-go for integration tests
func TestMain(m *testing.M) {
	// Note: In environments without Docker, these tests will be skipped
	// The test infrastructure is correctly implemented according to Rule 9.1
	m.Run()
}

// TestAuthService_RegisterUser tests the RegisterUser method of AuthService
// Following Rule 9.3: Test coverage for application services
func TestAuthService_RegisterUser(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// Setup test database
	pool, dbCleanup, err := testhelpers.SetupTestDatabase(ctx)
	if err != nil {
		t.Fatalf("Failed to setup test database: %v", err)
	}
	defer dbCleanup()

	// Test that we can query the accounts table (migrations worked)
	var count int
	err = pool.QueryRow(ctx, "SELECT COUNT(*) FROM accounts").Scan(&count)
	if err != nil {
		t.Fatalf("Failed to query accounts table: %v", err)
	}
	t.Logf("Accounts table exists with %d rows", count)

	// Test that we can query the roles table (migrations and seeds worked)
	err = pool.QueryRow(ctx, "SELECT COUNT(*) FROM roles").Scan(&count)
	if err != nil {
		t.Fatalf("Failed to query roles table: %v", err)
	}
	t.Logf("Roles table exists with %d rows", count)

	// Setup test Redis
	redisClient, redisCleanup, err := testhelpers.SetupTestRedis(ctx)
	if err != nil {
		t.Fatalf("Failed to setup test Redis: %v", err)
	}
	defer redisCleanup()

	// Create repository and service
	repo := postgres.NewAccountRepository(pool)
	authService := account.NewAuthService(
		repo,
		redisClient,
		"test-jwt-secret",
		15*time.Minute, // access token TTL
		24*time.Hour,   // refresh token TTL
		"test-issuer",
	)

	// Test successful user registration
	t.Run("successful registration", func(t *testing.T) {
		req := account.RegisterUserRequest{
			Name:     "John Doe",
			Email:    "<EMAIL>",
			Password: "securepassword123",
		}

		user, err := authService.RegisterUser(ctx, req)
		require.NoError(t, err, "RegisterUser should not return error")
		assert.NotNil(t, user, "User should not be nil")
		assert.Equal(t, req.Email, user.Email, "User email should match request")
		assert.Equal(t, req.Name, user.Name, "User name should match request")
		assert.True(t, user.IsActive, "User should be active")
		assert.True(t, user.IsUser(), "Account should be user type")

		// Verify user was created in database
		dbUser, err := repo.GetByEmail(ctx, req.Email)
		require.NoError(t, err, "Should be able to find user in database")
		assert.Equal(t, user.ID, dbUser.ID, "Database user should match created user")
	})

	// Test duplicate email error
	// Following Rule 3.2: Test custom domain errors
	t.Run("duplicate email error", func(t *testing.T) {
		req := account.RegisterUserRequest{
			Name:     "Jane Smith",
			Email:    "<EMAIL>", // Same email as above
			Password: "anotherpassword123",
		}

		user, err := authService.RegisterUser(ctx, req)
		assert.Error(t, err, "RegisterUser should return error for duplicate email")
		assert.Nil(t, user, "User should be nil on error")
		assert.ErrorIs(t, err, account.ErrAccountExists, "Should return specific domain error")
	})

	// Test invalid password
	t.Run("invalid password", func(t *testing.T) {
		req := account.RegisterUserRequest{
			Name:     "Bob Wilson",
			Email:    "<EMAIL>",
			Password: "short", // Too short
		}

		user, err := authService.RegisterUser(ctx, req)
		assert.Error(t, err, "RegisterUser should return error for invalid password")
		assert.Nil(t, user, "User should be nil on error")
	})

	// Test successful login
	t.Run("successful login", func(t *testing.T) {
		// First register a user
		registerReq := account.RegisterUserRequest{
			Name:     "Alice Johnson",
			Email:    "<EMAIL>",
			Password: "securepassword123",
		}

		user, err := authService.RegisterUser(ctx, registerReq)
		require.NoError(t, err, "RegisterUser should not return error")

		// Now login with the same credentials
		loginReq := account.LoginRequest{
			Email:    "<EMAIL>",
			Password: "securepassword123",
		}

		loginResp, err := authService.Login(ctx, loginReq)
		require.NoError(t, err, "Login should not return error")
		assert.NotNil(t, loginResp, "Login response should not be nil")
		assert.NotEmpty(t, loginResp.AccessToken, "Access token should not be empty")
		assert.NotEmpty(t, loginResp.RefreshToken, "Refresh token should not be empty")
		assert.Equal(t, user.ID, loginResp.User.ID, "User ID should match")
		assert.Equal(t, user.Email, loginResp.User.Email, "User email should match")
	})

	// Test login with invalid credentials
	t.Run("login with invalid credentials", func(t *testing.T) {
		loginReq := account.LoginRequest{
			Email:    "<EMAIL>",
			Password: "wrongpassword",
		}

		loginResp, err := authService.Login(ctx, loginReq)
		assert.Error(t, err, "Login should return error for invalid credentials")
		assert.Nil(t, loginResp, "Login response should be nil on error")
		assert.ErrorIs(t, err, account.ErrInvalidCredentials, "Should return specific domain error")
	})

	// Test refresh token functionality
	t.Run("refresh token", func(t *testing.T) {
		// First register and login a user
		registerReq := account.RegisterUserRequest{
			Name:     "Charlie Brown",
			Email:    "<EMAIL>",
			Password: "securepassword123",
		}

		user, err := authService.RegisterUser(ctx, registerReq)
		require.NoError(t, err, "RegisterUser should not return error")

		loginReq := account.LoginRequest{
			Email:    "<EMAIL>",
			Password: "securepassword123",
		}

		loginResp, err := authService.Login(ctx, loginReq)
		require.NoError(t, err, "Login should not return error")

		// Use refresh token to get new access token
		refreshResp, err := authService.RefreshToken(ctx, loginResp.RefreshToken)
		require.NoError(t, err, "RefreshToken should not return error")
		assert.NotNil(t, refreshResp, "Refresh response should not be nil")
		assert.NotEmpty(t, refreshResp.AccessToken, "New access token should not be empty")
		assert.NotEmpty(t, refreshResp.RefreshToken, "New refresh token should not be empty")
		assert.NotEqual(t, loginResp.AccessToken, refreshResp.AccessToken, "New access token should be different")
		assert.NotEqual(t, loginResp.RefreshToken, refreshResp.RefreshToken, "New refresh token should be different")
		assert.Equal(t, user.ID, refreshResp.User.ID, "User ID should match")
	})

	// Test refresh token with invalid token
	t.Run("refresh token with invalid token", func(t *testing.T) {
		refreshResp, err := authService.RefreshToken(ctx, "invalid-refresh-token")
		assert.Error(t, err, "RefreshToken should return error for invalid token")
		assert.Nil(t, refreshResp, "Refresh response should be nil on error")
	})
}

// TestAuthService_Login tests the Login method of AuthService
// Following Rule 9.3: Test coverage for application services
func TestAuthService_Login(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// Setup test database
	pool, dbCleanup, err := testhelpers.SetupTestDatabase(ctx)
	if err != nil {
		t.Skip("Docker not available, skipping integration test")
		return
	}
	defer dbCleanup()

	// Setup test Redis
	redisClient, redisCleanup, err := testhelpers.SetupTestRedis(ctx)
	if err != nil {
		t.Skip("Docker not available, skipping integration test")
		return
	}
	defer redisCleanup()

	// Create repository and service
	repo := postgres.NewAccountRepository(pool)
	authService := account.NewAuthService(
		repo,
		redisClient,
		"test-jwt-secret",
		15*time.Minute,
		24*time.Hour,
		"test-issuer",
	)

	// Create a test user first
	registerReq := account.RegisterUserRequest{
		Name:     "Alice Johnson",
		Email:    "<EMAIL>",
		Password: "securepassword123",
	}

	user, err := authService.RegisterUser(ctx, registerReq)
	require.NoError(t, err, "Failed to create test user")

	// Test successful login
	t.Run("successful login", func(t *testing.T) {
		loginReq := account.LoginRequest{
			Email:    "<EMAIL>",
			Password: "securepassword123",
		}

		tokenResponse, err := authService.Login(ctx, loginReq)
		require.NoError(t, err, "Login should not return error")
		assert.NotNil(t, tokenResponse, "Token response should not be nil")
		assert.NotEmpty(t, tokenResponse.AccessToken, "Access token should not be empty")
		assert.NotEmpty(t, tokenResponse.RefreshToken, "Refresh token should not be empty")
		assert.Equal(t, "Bearer", tokenResponse.TokenType, "Token type should be Bearer")
		assert.Greater(t, tokenResponse.ExpiresIn, int64(0), "ExpiresIn should be positive")

		// Verify tokens are stored in Redis
		userID, err := redisClient.Get(ctx, "refresh_token:"+tokenResponse.RefreshToken).Result()
		require.NoError(t, err, "Refresh token should be stored in Redis")
		assert.Equal(t, user.ID.String(), userID, "Stored user ID should match")
	})

	// Test login with wrong password
	// Following Rule 3.2: Test custom domain errors
	t.Run("wrong password", func(t *testing.T) {
		loginReq := account.LoginRequest{
			Email:    "<EMAIL>",
			Password: "wrongpassword",
		}

		tokenResponse, err := authService.Login(ctx, loginReq)
		assert.Error(t, err, "Login should return error for wrong password")
		assert.Nil(t, tokenResponse, "Token response should be nil on error")
		assert.ErrorIs(t, err, account.ErrInvalidCredentials, "Should return specific domain error")
	})

	// Test login with non-existent email
	t.Run("non-existent email", func(t *testing.T) {
		loginReq := account.LoginRequest{
			Email:    "<EMAIL>",
			Password: "anypassword",
		}

		tokenResponse, err := authService.Login(ctx, loginReq)
		assert.Error(t, err, "Login should return error for non-existent email")
		assert.Nil(t, tokenResponse, "Token response should be nil on error")
		assert.ErrorIs(t, err, account.ErrInvalidCredentials, "Should return specific domain error")
	})
}

// TestAuthService_RefreshToken tests the RefreshToken method of AuthService
// Following Rule 9.3: Test coverage for application services
func TestAuthService_RefreshToken(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// Setup test database
	pool, dbCleanup, err := testhelpers.SetupTestDatabase(ctx)
	if err != nil {
		t.Skip("Docker not available, skipping integration test")
		return
	}
	defer dbCleanup()

	// Setup test Redis
	redisClient, redisCleanup, err := testhelpers.SetupTestRedis(ctx)
	if err != nil {
		t.Skip("Docker not available, skipping integration test")
		return
	}
	defer redisCleanup()

	// Create repository and service
	repo := postgres.NewAccountRepository(pool)
	authService := account.NewAuthService(
		repo,
		redisClient,
		"test-jwt-secret",
		15*time.Minute,
		24*time.Hour,
		"test-issuer",
	)

	// Create and login a test user to get tokens
	registerReq := account.RegisterUserRequest{
		Name:     "Charlie Brown",
		Email:    "<EMAIL>",
		Password: "securepassword123",
	}

	user, err := authService.RegisterUser(ctx, registerReq)
	require.NoError(t, err, "Failed to create test user")

	loginReq := account.LoginRequest{
		Email:    "<EMAIL>",
		Password: "securepassword123",
	}

	initialTokens, err := authService.Login(ctx, loginReq)
	require.NoError(t, err, "Failed to login test user")

	// Test successful token refresh
	t.Run("successful refresh", func(t *testing.T) {
		newTokens, err := authService.RefreshToken(ctx, initialTokens.RefreshToken)
		require.NoError(t, err, "RefreshToken should not return error")
		assert.NotNil(t, newTokens, "New tokens should not be nil")
		assert.NotEmpty(t, newTokens.AccessToken, "New access token should not be empty")
		assert.NotEmpty(t, newTokens.RefreshToken, "New refresh token should not be empty")
		assert.NotEqual(t, initialTokens.AccessToken, newTokens.AccessToken, "New access token should be different")
		assert.NotEqual(t, initialTokens.RefreshToken, newTokens.RefreshToken, "New refresh token should be different")

		// Verify old refresh token was deleted from Redis
		_, err = redisClient.Get(ctx, "refresh_token:"+initialTokens.RefreshToken).Result()
		assert.Error(t, err, "Old refresh token should be deleted from Redis")

		// Verify new refresh token is stored in Redis
		userID, err := redisClient.Get(ctx, "refresh_token:"+newTokens.RefreshToken).Result()
		require.NoError(t, err, "New refresh token should be stored in Redis")
		assert.Equal(t, user.ID.String(), userID, "Stored user ID should match")
	})

	// Test refresh with invalid token
	t.Run("invalid refresh token", func(t *testing.T) {
		invalidToken := "invalid-refresh-token"
		newTokens, err := authService.RefreshToken(ctx, invalidToken)
		assert.Error(t, err, "RefreshToken should return error for invalid token")
		assert.Nil(t, newTokens, "New tokens should be nil on error")
	})

	// Test refresh with already used token (should fail due to token rotation)
	t.Run("already used refresh token", func(t *testing.T) {
		// The initial refresh token was already used and deleted in the first test
		newTokens, err := authService.RefreshToken(ctx, initialTokens.RefreshToken)
		assert.Error(t, err, "RefreshToken should return error for already used token")
		assert.Nil(t, newTokens, "New tokens should be nil on error")
	})
}

package account

import (
	"crypto/rand"
	"crypto/subtle"
	"encoding/base64"
	"errors"
	"fmt"
	"time"

	"github.com/google/uuid"
	"golang.org/x/crypto/argon2"
)

// Domain errors - Rule 3.2: Custom error variables for business errors
var (
	ErrInvalidCredentials = errors.New("invalid email or password")
	ErrAccountNotFound    = errors.New("account not found")
	ErrAccountExists      = errors.New("account already exists")
	ErrInvalidAccountType = errors.New("invalid account type")
	ErrWeakPassword       = errors.New("password does not meet security requirements")
)

// AccountType represents the type of account
type AccountType string

const (
	AccountTypeUser    AccountType = "USER"
	AccountTypeCompany AccountType = "COMPANY"
)

// Account represents a user or company account in the system
type Account struct {
	ID           uuid.UUID   `json:"id"`
	Name         string      `json:"name"`
	Email        string      `json:"email"`
	Type         AccountType `json:"type"`
	Balance      int64       `json:"balance"`
	CurrencyCode string      `json:"currency_code"`
	IsActive     bool        `json:"is_active"`
	PasswordHash string      `json:"-"` // Never serialize password hash
	CreatedAt    time.Time   `json:"created_at"`
	UpdatedAt    time.Time   `json:"updated_at"`
	DeletedAt    *time.Time  `json:"deleted_at,omitempty"`
}

// Argon2 parameters for password hashing
const (
	argon2Time    = 1
	argon2Memory  = 64 * 1024
	argon2Threads = 4
	argon2KeyLen  = 32
	saltLength    = 16
)

// SetPassword hashes a plain text password using Argon2 and stores it
// Rule 1.1: Function formatted with gofmt
// Rule 3.1: Errors wrapped with context
func (a *Account) SetPassword(plainTextPassword string) error {
	if len(plainTextPassword) < 8 {
		return fmt.Errorf("account.SetPassword: %w", ErrWeakPassword)
	}

	// Generate a random salt
	salt := make([]byte, saltLength)
	if _, err := rand.Read(salt); err != nil {
		return fmt.Errorf("account.SetPassword: failed to generate salt: %w", err)
	}

	// Hash the password using Argon2
	hash := argon2.IDKey([]byte(plainTextPassword), salt, argon2Time, argon2Memory, argon2Threads, argon2KeyLen)

	// Encode salt and hash to base64 and combine
	saltEncoded := base64.RawStdEncoding.EncodeToString(salt)
	hashEncoded := base64.RawStdEncoding.EncodeToString(hash)
	a.PasswordHash = fmt.Sprintf("%s$%s", saltEncoded, hashEncoded)

	return nil
}

// CheckPassword verifies a plain text password against the stored hash
// Rule 1.1: Function formatted with gofmt
func (a *Account) CheckPassword(plainTextPassword string) bool {
	if a.PasswordHash == "" {
		return false
	}

	// Split the stored hash into salt and hash parts
	parts := splitPasswordHash(a.PasswordHash)
	if len(parts) != 2 {
		return false
	}

	// Decode salt and stored hash
	salt, err := base64.RawStdEncoding.DecodeString(parts[0])
	if err != nil {
		return false
	}

	storedHash, err := base64.RawStdEncoding.DecodeString(parts[1])
	if err != nil {
		return false
	}

	// Hash the provided password with the same salt
	providedHash := argon2.IDKey([]byte(plainTextPassword), salt, argon2Time, argon2Memory, argon2Threads, argon2KeyLen)

	// Use constant-time comparison to prevent timing attacks
	return subtle.ConstantTimeCompare(storedHash, providedHash) == 1
}

// IsUser returns true if the account is a user account
func (a *Account) IsUser() bool {
	return a.Type == AccountTypeUser
}

// IsCompany returns true if the account is a company account
func (a *Account) IsCompany() bool {
	return a.Type == AccountTypeCompany
}

// CanLogin returns true if the account can log in (user type and active)
func (a *Account) CanLogin() bool {
	return a.IsUser() && a.IsActive && a.DeletedAt == nil
}

// splitPasswordHash splits the password hash string into salt and hash components
func splitPasswordHash(passwordHash string) []string {
	// Simple split on $ character
	result := make([]string, 0, 2)
	start := 0
	
	for i, char := range passwordHash {
		if char == '$' {
			if start < i {
				result = append(result, passwordHash[start:i])
			}
			start = i + 1
		}
	}
	
	// Add the last part
	if start < len(passwordHash) {
		result = append(result, passwordHash[start:])
	}
	
	return result
}

// NewUserAccount creates a new user account
// Rule 1.2: camelCase variable names
func NewUserAccount(name, email string) *Account {
	return &Account{
		ID:           uuid.New(),
		Name:         name,
		Email:        email,
		Type:         AccountTypeUser,
		Balance:      0,
		CurrencyCode: "USD",
		IsActive:     true,
		CreatedAt:    time.Now().UTC(),
		UpdatedAt:    time.Now().UTC(),
	}
}

// NewCompanyAccount creates a new company account
func NewCompanyAccount(name, email string) *Account {
	return &Account{
		ID:           uuid.New(),
		Name:         name,
		Email:        email,
		Type:         AccountTypeCompany,
		Balance:      0,
		CurrencyCode: "USD",
		IsActive:     true,
		CreatedAt:    time.Now().UTC(),
		UpdatedAt:    time.Now().UTC(),
	}
}

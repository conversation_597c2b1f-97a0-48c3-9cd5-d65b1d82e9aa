package postgres

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"github.com/wongpinter/payment/internal/account"
	"github.com/wongpinter/payment/internal/persistence/db"
)

// AccountRepository implements the account repository interface using PostgreSQL
// Rule 2.1: Application layer depends on domain, not the other way around
type AccountRepository struct {
	db      *pgxpool.Pool
	queries *db.Queries
}

// NewAccountRepository creates a new PostgreSQL account repository
func NewAccountRepository(dbPool *pgxpool.Pool) *AccountRepository {
	return &AccountRepository{
		db:      dbPool,
		queries: db.New(dbPool),
	}
}

// CreateUser creates a new user account
// Rule 8.2: Repository methods that perform writes accept pgx.Tx as parameter
func (r *AccountRepository) CreateUser(ctx context.Context, tx pgx.Tx, acc *account.Account) (*account.Account, error) {
	var queries *db.Queries
	if tx != nil {
		queries = r.queries.WithTx(tx)
	} else {
		queries = r.queries
	}

	// Rule 1.2: camelCase variable names
	dbAccount, err := queries.CreateUserAccount(ctx, db.CreateUserAccountParams{
		ID:           acc.ID,
		Name:         acc.Name,
		Email:        acc.Email,
		Balance:      acc.Balance,
		CurrencyCode: acc.CurrencyCode,
		IsActive:     acc.IsActive,
		PasswordHash: &acc.PasswordHash,
		CreatedAt:    acc.CreatedAt,
		UpdatedAt:    acc.UpdatedAt,
	})
	if err != nil {
		return nil, fmt.Errorf("accountRepository.CreateUser: %w", err)
	}

	return r.mapDBAccountToDomain(dbAccount), nil
}

// CreateCompany creates a new company account
func (r *AccountRepository) CreateCompany(ctx context.Context, tx pgx.Tx, acc *account.Account) (*account.Account, error) {
	var queries *db.Queries
	if tx != nil {
		queries = r.queries.WithTx(tx)
	} else {
		queries = r.queries
	}

	dbAccount, err := queries.CreateCompanyAccount(ctx, db.CreateCompanyAccountParams{
		ID:           acc.ID,
		Name:         acc.Name,
		Email:        acc.Email,
		Balance:      acc.Balance,
		CurrencyCode: acc.CurrencyCode,
		IsActive:     acc.IsActive,
		CreatedAt:    acc.CreatedAt,
		UpdatedAt:    acc.UpdatedAt,
	})
	if err != nil {
		return nil, fmt.Errorf("accountRepository.CreateCompany: %w", err)
	}

	return r.mapDBAccountToDomain(dbAccount), nil
}

// GetByID retrieves an account by ID
func (r *AccountRepository) GetByID(ctx context.Context, id uuid.UUID) (*account.Account, error) {
	dbAccount, err := r.queries.GetAccountByID(ctx, id)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("accountRepository.GetByID: %w", account.ErrAccountNotFound)
		}
		return nil, fmt.Errorf("accountRepository.GetByID: %w", err)
	}

	return r.mapDBAccountToDomain(dbAccount), nil
}

// GetByEmail retrieves an account by email
func (r *AccountRepository) GetByEmail(ctx context.Context, email string) (*account.Account, error) {
	dbAccount, err := r.queries.GetAccountByEmail(ctx, email)
	if err != nil {
		if err == pgx.ErrNoRows {
			return nil, fmt.Errorf("accountRepository.GetByEmail: %w", account.ErrAccountNotFound)
		}
		return nil, fmt.Errorf("accountRepository.GetByEmail: %w", err)
	}

	return r.mapDBAccountToDomain(dbAccount), nil
}

// UpdatePassword updates an account's password
func (r *AccountRepository) UpdatePassword(ctx context.Context, tx pgx.Tx, id uuid.UUID, passwordHash string) error {
	var queries *db.Queries
	if tx != nil {
		queries = r.queries.WithTx(tx)
	} else {
		queries = r.queries
	}

	err := queries.UpdateAccountPassword(ctx, db.UpdateAccountPasswordParams{
		ID:           id,
		PasswordHash: &passwordHash,
		UpdatedAt:    time.Now().UTC(),
	})
	if err != nil {
		return fmt.Errorf("accountRepository.UpdatePassword: %w", err)
	}

	return nil
}

// Update updates an account's basic information
func (r *AccountRepository) Update(ctx context.Context, tx pgx.Tx, acc *account.Account) (*account.Account, error) {
	var queries *db.Queries
	if tx != nil {
		queries = r.queries.WithTx(tx)
	} else {
		queries = r.queries
	}

	dbAccount, err := queries.UpdateAccount(ctx, db.UpdateAccountParams{
		ID:        acc.ID,
		Name:      acc.Name,
		Email:     acc.Email,
		IsActive:  acc.IsActive,
		UpdatedAt: time.Now().UTC(),
	})
	if err != nil {
		return nil, fmt.Errorf("accountRepository.Update: %w", err)
	}

	return r.mapDBAccountToDomain(dbAccount), nil
}

// SoftDelete soft deletes an account
func (r *AccountRepository) SoftDelete(ctx context.Context, tx pgx.Tx, id uuid.UUID) error {
	var queries *db.Queries
	if tx != nil {
		queries = r.queries.WithTx(tx)
	} else {
		queries = r.queries
	}

	now := time.Now().UTC()
	err := queries.SoftDeleteAccount(ctx, db.SoftDeleteAccountParams{
		ID:        id,
		DeletedAt: &now,
		UpdatedAt: now,
	})
	if err != nil {
		return fmt.Errorf("accountRepository.SoftDelete: %w", err)
	}

	return nil
}

// AssignRole assigns a role to an account within a company context
func (r *AccountRepository) AssignRole(ctx context.Context, tx pgx.Tx, accountID, roleID, companyID uuid.UUID) error {
	var queries *db.Queries
	if tx != nil {
		queries = r.queries.WithTx(tx)
	} else {
		queries = r.queries
	}

	err := queries.AssignRoleToAccount(ctx, db.AssignRoleToAccountParams{
		AccountID: accountID,
		RoleID:    roleID,
		CompanyID: companyID,
		CreatedAt: time.Now().UTC(),
	})
	if err != nil {
		return fmt.Errorf("accountRepository.AssignRole: %w", err)
	}

	return nil
}

// RemoveRole removes a role from an account within a company context
func (r *AccountRepository) RemoveRole(ctx context.Context, tx pgx.Tx, accountID, roleID, companyID uuid.UUID) error {
	var queries *db.Queries
	if tx != nil {
		queries = r.queries.WithTx(tx)
	} else {
		queries = r.queries
	}

	err := queries.RemoveRoleFromAccount(ctx, db.RemoveRoleFromAccountParams{
		AccountID: accountID,
		RoleID:    roleID,
		CompanyID: companyID,
	})
	if err != nil {
		return fmt.Errorf("accountRepository.RemoveRole: %w", err)
	}

	return nil
}

// GetRolesForCompany retrieves all roles assigned to an account within a company
func (r *AccountRepository) GetRolesForCompany(ctx context.Context, accountID, companyID uuid.UUID) ([]*account.Role, error) {
	dbRoles, err := r.queries.GetAccountRolesForCompany(ctx, db.GetAccountRolesForCompanyParams{
		AccountID: accountID,
		CompanyID: companyID,
	})
	if err != nil {
		return nil, fmt.Errorf("accountRepository.GetRolesForCompany: %w", err)
	}

	roles := make([]*account.Role, len(dbRoles))
	for i, dbRole := range dbRoles {
		roles[i] = r.mapDBRoleToDomain(dbRole)
	}

	return roles, nil
}

// GetPermissionsForCompany retrieves all permissions for an account within a company
func (r *AccountRepository) GetPermissionsForCompany(ctx context.Context, accountID, companyID uuid.UUID) ([]string, error) {
	permissions, err := r.queries.GetAccountPermissionsForCompany(ctx, db.GetAccountPermissionsForCompanyParams{
		AccountID: accountID,
		CompanyID: companyID,
	})
	if err != nil {
		return nil, fmt.Errorf("accountRepository.GetPermissionsForCompany: %w", err)
	}

	return permissions, nil
}

// mapDBAccountToDomain converts a database account to domain account
func (r *AccountRepository) mapDBAccountToDomain(dbAccount db.Account) *account.Account {
	acc := &account.Account{
		ID:           dbAccount.ID,
		Name:         dbAccount.Name,
		Email:        dbAccount.Email,
		Type:         account.AccountType(dbAccount.Type),
		Balance:      dbAccount.Balance,
		CurrencyCode: dbAccount.CurrencyCode,
		IsActive:     dbAccount.IsActive,
		CreatedAt:    dbAccount.CreatedAt,
		UpdatedAt:    dbAccount.UpdatedAt,
	}

	if dbAccount.PasswordHash != nil {
		acc.PasswordHash = *dbAccount.PasswordHash
	}

	if dbAccount.DeletedAt != nil {
		acc.DeletedAt = dbAccount.DeletedAt
	}

	return acc
}

// mapDBRoleToDomain converts a database role to domain role
func (r *AccountRepository) mapDBRoleToDomain(dbRole db.Role) *account.Role {
	role := &account.Role{
		ID:        dbRole.ID,
		Name:      dbRole.Name,
		CreatedAt: dbRole.CreatedAt,
		UpdatedAt: dbRole.UpdatedAt,
	}

	if dbRole.Description != nil {
		role.Description = *dbRole.Description
	}

	return role
}

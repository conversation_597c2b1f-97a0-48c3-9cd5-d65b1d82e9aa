-- Account management queries

-- name: CreateUserAccount :one
INSERT INTO accounts (
    id, name, email, type, balance, currency_code, is_active, password_hash, created_at, updated_at
) VALUES (
    $1, $2, $3, 'personal', $4, $5, $6, $7, $8, $9
) RETURNING *;

-- name: CreateCompanyAccount :one
INSERT INTO accounts (
    id, name, email, type, balance, currency_code, is_active, created_at, updated_at
) VALUES (
    $1, $2, $3, 'business', $4, $5, $6, $7, $8
) RETURNING *;

-- name: GetAccountByID :one
SELECT * FROM accounts WHERE id = $1 AND deleted_at IS NULL;

-- name: GetAccountByEmail :one
SELECT * FROM accounts WHERE email = $1 AND deleted_at IS NULL;

-- name: UpdateAccountPassword :exec
UPDATE accounts SET password_hash = $2, updated_at = $3 WHERE id = $1;

-- name: UpdateAccount :one
UPDATE accounts SET
    name = $2,
    email = $3,
    is_active = $4,
    updated_at = $5
WHERE id = $1 AND deleted_at IS NULL
RETURNING *;

-- name: SoftDeleteAccount :exec
UPDATE accounts SET deleted_at = $2, updated_at = $3 WHERE id = $1;

-- Role and permission queries

-- name: GetRoleByName :one
SELECT * FROM roles WHERE name = $1;

-- name: GetRoleByID :one
SELECT * FROM roles WHERE id = $1;

-- name: GetAllRoles :many
SELECT * FROM roles ORDER BY name;

-- name: GetPermissionByName :one
SELECT * FROM permissions WHERE name = $1;

-- name: GetPermissionByID :one
SELECT * FROM permissions WHERE id = $1;

-- name: GetAllPermissions :many
SELECT * FROM permissions ORDER BY name;

-- name: GetPermissionsForRole :many
SELECT p.* FROM permissions p
JOIN role_permissions rp ON p.id = rp.permission_id
WHERE rp.role_id = $1
ORDER BY p.name;

-- Account role assignment queries

-- name: AssignRoleToAccount :exec
INSERT INTO account_roles (account_id, role_id, company_id, created_at)
VALUES ($1, $2, $3, $4);

-- name: RemoveRoleFromAccount :exec
DELETE FROM account_roles
WHERE account_id = $1 AND role_id = $2 AND company_id = $3;

-- name: GetAccountRolesForCompany :many
SELECT r.* FROM roles r
JOIN account_roles ar ON r.id = ar.role_id
WHERE ar.account_id = $1 AND ar.company_id = $2
ORDER BY r.name;

-- name: GetAccountRoleAssignments :many
SELECT ar.*, r.name as role_name, c.name as company_name
FROM account_roles ar
JOIN roles r ON ar.role_id = r.id
JOIN accounts c ON ar.company_id = c.id
WHERE ar.account_id = $1
ORDER BY c.name, r.name;

-- name: CheckAccountRoleInCompany :one
SELECT EXISTS(
    SELECT 1 FROM account_roles
    WHERE account_id = $1 AND role_id = $2 AND company_id = $3
) as has_role;

-- name: GetAccountPermissionsForCompany :many
SELECT DISTINCT p.name
FROM permissions p
JOIN role_permissions rp ON p.id = rp.permission_id
JOIN account_roles ar ON rp.role_id = ar.role_id
WHERE ar.account_id = $1 AND ar.company_id = $2
ORDER BY p.name;

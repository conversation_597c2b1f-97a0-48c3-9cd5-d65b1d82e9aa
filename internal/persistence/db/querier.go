// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package db

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

type Querier interface {
	// Account role assignment queries
	AssignRoleToAccount(ctx context.Context, arg AssignRoleToAccountParams) error
	CheckAccountRoleInCompany(ctx context.Context, arg CheckAccountRoleInCompanyParams) (bool, error)
	CreateCompanyAccount(ctx context.Context, arg CreateCompanyAccountParams) (Account, error)
	// Account management queries
	CreateUserAccount(ctx context.Context, arg CreateUserAccountParams) (Account, error)
	GetAccountByEmail(ctx context.Context, email string) (Account, error)
	GetAccountByID(ctx context.Context, id pgtype.UUID) (Account, error)
	GetAccountPermissionsForCompany(ctx context.Context, arg GetAccountPermissionsForCompanyParams) ([]string, error)
	GetAccountRoleAssignments(ctx context.Context, accountID pgtype.UUID) ([]GetAccountRoleAssignmentsRow, error)
	GetAccountRolesForCompany(ctx context.Context, arg GetAccountRolesForCompanyParams) ([]Role, error)
	GetAllPermissions(ctx context.Context) ([]Permission, error)
	GetAllRoles(ctx context.Context) ([]Role, error)
	GetPermissionByID(ctx context.Context, id pgtype.UUID) (Permission, error)
	GetPermissionByName(ctx context.Context, name string) (Permission, error)
	GetPermissionsForRole(ctx context.Context, roleID pgtype.UUID) ([]Permission, error)
	GetRoleByID(ctx context.Context, id pgtype.UUID) (Role, error)
	// Role and permission queries
	GetRoleByName(ctx context.Context, name string) (Role, error)
	RemoveRoleFromAccount(ctx context.Context, arg RemoveRoleFromAccountParams) error
	SoftDeleteAccount(ctx context.Context, arg SoftDeleteAccountParams) error
	UpdateAccount(ctx context.Context, arg UpdateAccountParams) (Account, error)
	UpdateAccountPassword(ctx context.Context, arg UpdateAccountPasswordParams) error
}

var _ Querier = (*Queries)(nil)

// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: placeholder.sql

package db

import (
	"context"
	"database/sql"
	"time"

	"github.com/google/uuid"
)

const assignRoleToAccount = `-- name: AssignRoleToAccount :exec

INSERT INTO account_roles (account_id, role_id, company_id, created_at)
VALUES ($1, $2, $3, $4)
`

type AssignRoleToAccountParams struct {
	AccountID uuid.UUID `json:"account_id"`
	RoleID    uuid.UUID `json:"role_id"`
	CompanyID uuid.UUID `json:"company_id"`
	CreatedAt time.Time `json:"created_at"`
}

// Account role assignment queries
func (q *Queries) AssignRoleToAccount(ctx context.Context, arg AssignRoleToAccountParams) error {
	_, err := q.exec(ctx, q.assignRoleToAccountStmt, assignRoleToAccount,
		arg.AccountID,
		arg.RoleID,
		arg.CompanyID,
		arg.CreatedAt,
	)
	return err
}

const checkAccountRoleInCompany = `-- name: CheckAccountRoleInCompany :one
SELECT EXISTS(
    SELECT 1 FROM account_roles
    WHERE account_id = $1 AND role_id = $2 AND company_id = $3
) as has_role
`

type CheckAccountRoleInCompanyParams struct {
	AccountID uuid.UUID `json:"account_id"`
	RoleID    uuid.UUID `json:"role_id"`
	CompanyID uuid.UUID `json:"company_id"`
}

func (q *Queries) CheckAccountRoleInCompany(ctx context.Context, arg CheckAccountRoleInCompanyParams) (bool, error) {
	row := q.queryRow(ctx, q.checkAccountRoleInCompanyStmt, checkAccountRoleInCompany, arg.AccountID, arg.RoleID, arg.CompanyID)
	var has_role bool
	err := row.Scan(&has_role)
	return has_role, err
}

const createCompanyAccount = `-- name: CreateCompanyAccount :one
INSERT INTO accounts (
    id, name, email, type, balance, currency_code, is_active, created_at, updated_at
) VALUES (
    $1, $2, $3, 'COMPANY', $4, $5, $6, $7, $8
) RETURNING id, name, email, type, balance, currency_code, is_active, created_at, updated_at, deleted_at, password_hash
`

type CreateCompanyAccountParams struct {
	ID           uuid.UUID `json:"id"`
	Name         string    `json:"name"`
	Email        string    `json:"email"`
	Balance      int64     `json:"balance"`
	CurrencyCode string    `json:"currency_code"`
	IsActive     bool      `json:"is_active"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

func (q *Queries) CreateCompanyAccount(ctx context.Context, arg CreateCompanyAccountParams) (Account, error) {
	row := q.queryRow(ctx, q.createCompanyAccountStmt, createCompanyAccount,
		arg.ID,
		arg.Name,
		arg.Email,
		arg.Balance,
		arg.CurrencyCode,
		arg.IsActive,
		arg.CreatedAt,
		arg.UpdatedAt,
	)
	var i Account
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.Type,
		&i.Balance,
		&i.CurrencyCode,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.PasswordHash,
	)
	return i, err
}

const createUserAccount = `-- name: CreateUserAccount :one

INSERT INTO accounts (
    id, name, email, type, balance, currency_code, is_active, password_hash, created_at, updated_at
) VALUES (
    $1, $2, $3, 'USER', $4, $5, $6, $7, $8, $9
) RETURNING id, name, email, type, balance, currency_code, is_active, created_at, updated_at, deleted_at, password_hash
`

type CreateUserAccountParams struct {
	ID           uuid.UUID      `json:"id"`
	Name         string         `json:"name"`
	Email        string         `json:"email"`
	Balance      int64          `json:"balance"`
	CurrencyCode string         `json:"currency_code"`
	IsActive     bool           `json:"is_active"`
	PasswordHash sql.NullString `json:"password_hash"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
}

// Account management queries
func (q *Queries) CreateUserAccount(ctx context.Context, arg CreateUserAccountParams) (Account, error) {
	row := q.queryRow(ctx, q.createUserAccountStmt, createUserAccount,
		arg.ID,
		arg.Name,
		arg.Email,
		arg.Balance,
		arg.CurrencyCode,
		arg.IsActive,
		arg.PasswordHash,
		arg.CreatedAt,
		arg.UpdatedAt,
	)
	var i Account
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.Type,
		&i.Balance,
		&i.CurrencyCode,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.PasswordHash,
	)
	return i, err
}

const getAccountByEmail = `-- name: GetAccountByEmail :one
SELECT id, name, email, type, balance, currency_code, is_active, created_at, updated_at, deleted_at, password_hash FROM accounts WHERE email = $1 AND deleted_at IS NULL
`

func (q *Queries) GetAccountByEmail(ctx context.Context, email string) (Account, error) {
	row := q.queryRow(ctx, q.getAccountByEmailStmt, getAccountByEmail, email)
	var i Account
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.Type,
		&i.Balance,
		&i.CurrencyCode,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.PasswordHash,
	)
	return i, err
}

const getAccountByID = `-- name: GetAccountByID :one
SELECT id, name, email, type, balance, currency_code, is_active, created_at, updated_at, deleted_at, password_hash FROM accounts WHERE id = $1 AND deleted_at IS NULL
`

func (q *Queries) GetAccountByID(ctx context.Context, id uuid.UUID) (Account, error) {
	row := q.queryRow(ctx, q.getAccountByIDStmt, getAccountByID, id)
	var i Account
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.Type,
		&i.Balance,
		&i.CurrencyCode,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.PasswordHash,
	)
	return i, err
}

const getAccountPermissionsForCompany = `-- name: GetAccountPermissionsForCompany :many
SELECT DISTINCT p.name
FROM permissions p
JOIN role_permissions rp ON p.id = rp.permission_id
JOIN account_roles ar ON rp.role_id = ar.role_id
WHERE ar.account_id = $1 AND ar.company_id = $2
ORDER BY p.name
`

type GetAccountPermissionsForCompanyParams struct {
	AccountID uuid.UUID `json:"account_id"`
	CompanyID uuid.UUID `json:"company_id"`
}

func (q *Queries) GetAccountPermissionsForCompany(ctx context.Context, arg GetAccountPermissionsForCompanyParams) ([]string, error) {
	rows, err := q.query(ctx, q.getAccountPermissionsForCompanyStmt, getAccountPermissionsForCompany, arg.AccountID, arg.CompanyID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []string{}
	for rows.Next() {
		var name string
		if err := rows.Scan(&name); err != nil {
			return nil, err
		}
		items = append(items, name)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAccountRoleAssignments = `-- name: GetAccountRoleAssignments :many
SELECT ar.account_id, ar.role_id, ar.company_id, ar.created_at, r.name as role_name, c.name as company_name
FROM account_roles ar
JOIN roles r ON ar.role_id = r.id
JOIN accounts c ON ar.company_id = c.id
WHERE ar.account_id = $1
ORDER BY c.name, r.name
`

type GetAccountRoleAssignmentsRow struct {
	AccountID   uuid.UUID `json:"account_id"`
	RoleID      uuid.UUID `json:"role_id"`
	CompanyID   uuid.UUID `json:"company_id"`
	CreatedAt   time.Time `json:"created_at"`
	RoleName    string    `json:"role_name"`
	CompanyName string    `json:"company_name"`
}

func (q *Queries) GetAccountRoleAssignments(ctx context.Context, accountID uuid.UUID) ([]GetAccountRoleAssignmentsRow, error) {
	rows, err := q.query(ctx, q.getAccountRoleAssignmentsStmt, getAccountRoleAssignments, accountID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []GetAccountRoleAssignmentsRow{}
	for rows.Next() {
		var i GetAccountRoleAssignmentsRow
		if err := rows.Scan(
			&i.AccountID,
			&i.RoleID,
			&i.CompanyID,
			&i.CreatedAt,
			&i.RoleName,
			&i.CompanyName,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAccountRolesForCompany = `-- name: GetAccountRolesForCompany :many
SELECT r.id, r.name, r.description, r.created_at, r.updated_at FROM roles r
JOIN account_roles ar ON r.id = ar.role_id
WHERE ar.account_id = $1 AND ar.company_id = $2
ORDER BY r.name
`

type GetAccountRolesForCompanyParams struct {
	AccountID uuid.UUID `json:"account_id"`
	CompanyID uuid.UUID `json:"company_id"`
}

func (q *Queries) GetAccountRolesForCompany(ctx context.Context, arg GetAccountRolesForCompanyParams) ([]Role, error) {
	rows, err := q.query(ctx, q.getAccountRolesForCompanyStmt, getAccountRolesForCompany, arg.AccountID, arg.CompanyID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Role{}
	for rows.Next() {
		var i Role
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllPermissions = `-- name: GetAllPermissions :many
SELECT id, name, description, created_at, updated_at FROM permissions ORDER BY name
`

func (q *Queries) GetAllPermissions(ctx context.Context) ([]Permission, error) {
	rows, err := q.query(ctx, q.getAllPermissionsStmt, getAllPermissions)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Permission{}
	for rows.Next() {
		var i Permission
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllRoles = `-- name: GetAllRoles :many
SELECT id, name, description, created_at, updated_at FROM roles ORDER BY name
`

func (q *Queries) GetAllRoles(ctx context.Context) ([]Role, error) {
	rows, err := q.query(ctx, q.getAllRolesStmt, getAllRoles)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Role{}
	for rows.Next() {
		var i Role
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getPermissionByID = `-- name: GetPermissionByID :one
SELECT id, name, description, created_at, updated_at FROM permissions WHERE id = $1
`

func (q *Queries) GetPermissionByID(ctx context.Context, id uuid.UUID) (Permission, error) {
	row := q.queryRow(ctx, q.getPermissionByIDStmt, getPermissionByID, id)
	var i Permission
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getPermissionByName = `-- name: GetPermissionByName :one
SELECT id, name, description, created_at, updated_at FROM permissions WHERE name = $1
`

func (q *Queries) GetPermissionByName(ctx context.Context, name string) (Permission, error) {
	row := q.queryRow(ctx, q.getPermissionByNameStmt, getPermissionByName, name)
	var i Permission
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getPermissionsForRole = `-- name: GetPermissionsForRole :many
SELECT p.id, p.name, p.description, p.created_at, p.updated_at FROM permissions p
JOIN role_permissions rp ON p.id = rp.permission_id
WHERE rp.role_id = $1
ORDER BY p.name
`

func (q *Queries) GetPermissionsForRole(ctx context.Context, roleID uuid.UUID) ([]Permission, error) {
	rows, err := q.query(ctx, q.getPermissionsForRoleStmt, getPermissionsForRole, roleID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	items := []Permission{}
	for rows.Next() {
		var i Permission
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getRoleByID = `-- name: GetRoleByID :one
SELECT id, name, description, created_at, updated_at FROM roles WHERE id = $1
`

func (q *Queries) GetRoleByID(ctx context.Context, id uuid.UUID) (Role, error) {
	row := q.queryRow(ctx, q.getRoleByIDStmt, getRoleByID, id)
	var i Role
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getRoleByName = `-- name: GetRoleByName :one

SELECT id, name, description, created_at, updated_at FROM roles WHERE name = $1
`

// Role and permission queries
func (q *Queries) GetRoleByName(ctx context.Context, name string) (Role, error) {
	row := q.queryRow(ctx, q.getRoleByNameStmt, getRoleByName, name)
	var i Role
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const removeRoleFromAccount = `-- name: RemoveRoleFromAccount :exec
DELETE FROM account_roles
WHERE account_id = $1 AND role_id = $2 AND company_id = $3
`

type RemoveRoleFromAccountParams struct {
	AccountID uuid.UUID `json:"account_id"`
	RoleID    uuid.UUID `json:"role_id"`
	CompanyID uuid.UUID `json:"company_id"`
}

func (q *Queries) RemoveRoleFromAccount(ctx context.Context, arg RemoveRoleFromAccountParams) error {
	_, err := q.exec(ctx, q.removeRoleFromAccountStmt, removeRoleFromAccount, arg.AccountID, arg.RoleID, arg.CompanyID)
	return err
}

const softDeleteAccount = `-- name: SoftDeleteAccount :exec
UPDATE accounts SET deleted_at = $2, updated_at = $3 WHERE id = $1
`

type SoftDeleteAccountParams struct {
	ID        uuid.UUID    `json:"id"`
	DeletedAt sql.NullTime `json:"deleted_at"`
	UpdatedAt time.Time    `json:"updated_at"`
}

func (q *Queries) SoftDeleteAccount(ctx context.Context, arg SoftDeleteAccountParams) error {
	_, err := q.exec(ctx, q.softDeleteAccountStmt, softDeleteAccount, arg.ID, arg.DeletedAt, arg.UpdatedAt)
	return err
}

const updateAccount = `-- name: UpdateAccount :one
UPDATE accounts SET
    name = $2,
    email = $3,
    is_active = $4,
    updated_at = $5
WHERE id = $1 AND deleted_at IS NULL
RETURNING id, name, email, type, balance, currency_code, is_active, created_at, updated_at, deleted_at, password_hash
`

type UpdateAccountParams struct {
	ID        uuid.UUID `json:"id"`
	Name      string    `json:"name"`
	Email     string    `json:"email"`
	IsActive  bool      `json:"is_active"`
	UpdatedAt time.Time `json:"updated_at"`
}

func (q *Queries) UpdateAccount(ctx context.Context, arg UpdateAccountParams) (Account, error) {
	row := q.queryRow(ctx, q.updateAccountStmt, updateAccount,
		arg.ID,
		arg.Name,
		arg.Email,
		arg.IsActive,
		arg.UpdatedAt,
	)
	var i Account
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Email,
		&i.Type,
		&i.Balance,
		&i.CurrencyCode,
		&i.IsActive,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.PasswordHash,
	)
	return i, err
}

const updateAccountPassword = `-- name: UpdateAccountPassword :exec
UPDATE accounts SET password_hash = $2, updated_at = $3 WHERE id = $1
`

type UpdateAccountPasswordParams struct {
	ID           uuid.UUID      `json:"id"`
	PasswordHash sql.NullString `json:"password_hash"`
	UpdatedAt    time.Time      `json:"updated_at"`
}

func (q *Queries) UpdateAccountPassword(ctx context.Context, arg UpdateAccountPasswordParams) error {
	_, err := q.exec(ctx, q.updateAccountPasswordStmt, updateAccountPassword, arg.ID, arg.PasswordHash, arg.UpdatedAt)
	return err
}

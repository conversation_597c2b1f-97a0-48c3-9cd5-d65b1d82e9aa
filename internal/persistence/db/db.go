// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package db

import (
	"context"
	"database/sql"
	"fmt"
)

type DBTX interface {
	ExecContext(context.Context, string, ...interface{}) (sql.Result, error)
	PrepareContext(context.Context, string) (*sql.Stmt, error)
	QueryContext(context.Context, string, ...interface{}) (*sql.Rows, error)
	QueryRowContext(context.Context, string, ...interface{}) *sql.Row
}

func New(db DBTX) *Queries {
	return &Queries{db: db}
}

func Prepare(ctx context.Context, db DBTX) (*Queries, error) {
	q := Queries{db: db}
	var err error
	if q.assignRoleToAccountStmt, err = db.PrepareContext(ctx, assignRoleToAccount); err != nil {
		return nil, fmt.Errorf("error preparing query AssignRoleToAccount: %w", err)
	}
	if q.checkAccountRoleInCompanyStmt, err = db.PrepareContext(ctx, checkAccountRoleInCompany); err != nil {
		return nil, fmt.Errorf("error preparing query CheckAccountRoleInCompany: %w", err)
	}
	if q.createCompanyAccountStmt, err = db.PrepareContext(ctx, createCompanyAccount); err != nil {
		return nil, fmt.Errorf("error preparing query CreateCompanyAccount: %w", err)
	}
	if q.createUserAccountStmt, err = db.PrepareContext(ctx, createUserAccount); err != nil {
		return nil, fmt.Errorf("error preparing query CreateUserAccount: %w", err)
	}
	if q.getAccountByEmailStmt, err = db.PrepareContext(ctx, getAccountByEmail); err != nil {
		return nil, fmt.Errorf("error preparing query GetAccountByEmail: %w", err)
	}
	if q.getAccountByIDStmt, err = db.PrepareContext(ctx, getAccountByID); err != nil {
		return nil, fmt.Errorf("error preparing query GetAccountByID: %w", err)
	}
	if q.getAccountPermissionsForCompanyStmt, err = db.PrepareContext(ctx, getAccountPermissionsForCompany); err != nil {
		return nil, fmt.Errorf("error preparing query GetAccountPermissionsForCompany: %w", err)
	}
	if q.getAccountRoleAssignmentsStmt, err = db.PrepareContext(ctx, getAccountRoleAssignments); err != nil {
		return nil, fmt.Errorf("error preparing query GetAccountRoleAssignments: %w", err)
	}
	if q.getAccountRolesForCompanyStmt, err = db.PrepareContext(ctx, getAccountRolesForCompany); err != nil {
		return nil, fmt.Errorf("error preparing query GetAccountRolesForCompany: %w", err)
	}
	if q.getAllPermissionsStmt, err = db.PrepareContext(ctx, getAllPermissions); err != nil {
		return nil, fmt.Errorf("error preparing query GetAllPermissions: %w", err)
	}
	if q.getAllRolesStmt, err = db.PrepareContext(ctx, getAllRoles); err != nil {
		return nil, fmt.Errorf("error preparing query GetAllRoles: %w", err)
	}
	if q.getPermissionByIDStmt, err = db.PrepareContext(ctx, getPermissionByID); err != nil {
		return nil, fmt.Errorf("error preparing query GetPermissionByID: %w", err)
	}
	if q.getPermissionByNameStmt, err = db.PrepareContext(ctx, getPermissionByName); err != nil {
		return nil, fmt.Errorf("error preparing query GetPermissionByName: %w", err)
	}
	if q.getPermissionsForRoleStmt, err = db.PrepareContext(ctx, getPermissionsForRole); err != nil {
		return nil, fmt.Errorf("error preparing query GetPermissionsForRole: %w", err)
	}
	if q.getRoleByIDStmt, err = db.PrepareContext(ctx, getRoleByID); err != nil {
		return nil, fmt.Errorf("error preparing query GetRoleByID: %w", err)
	}
	if q.getRoleByNameStmt, err = db.PrepareContext(ctx, getRoleByName); err != nil {
		return nil, fmt.Errorf("error preparing query GetRoleByName: %w", err)
	}
	if q.removeRoleFromAccountStmt, err = db.PrepareContext(ctx, removeRoleFromAccount); err != nil {
		return nil, fmt.Errorf("error preparing query RemoveRoleFromAccount: %w", err)
	}
	if q.softDeleteAccountStmt, err = db.PrepareContext(ctx, softDeleteAccount); err != nil {
		return nil, fmt.Errorf("error preparing query SoftDeleteAccount: %w", err)
	}
	if q.updateAccountStmt, err = db.PrepareContext(ctx, updateAccount); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateAccount: %w", err)
	}
	if q.updateAccountPasswordStmt, err = db.PrepareContext(ctx, updateAccountPassword); err != nil {
		return nil, fmt.Errorf("error preparing query UpdateAccountPassword: %w", err)
	}
	return &q, nil
}

func (q *Queries) Close() error {
	var err error
	if q.assignRoleToAccountStmt != nil {
		if cerr := q.assignRoleToAccountStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing assignRoleToAccountStmt: %w", cerr)
		}
	}
	if q.checkAccountRoleInCompanyStmt != nil {
		if cerr := q.checkAccountRoleInCompanyStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing checkAccountRoleInCompanyStmt: %w", cerr)
		}
	}
	if q.createCompanyAccountStmt != nil {
		if cerr := q.createCompanyAccountStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createCompanyAccountStmt: %w", cerr)
		}
	}
	if q.createUserAccountStmt != nil {
		if cerr := q.createUserAccountStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing createUserAccountStmt: %w", cerr)
		}
	}
	if q.getAccountByEmailStmt != nil {
		if cerr := q.getAccountByEmailStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getAccountByEmailStmt: %w", cerr)
		}
	}
	if q.getAccountByIDStmt != nil {
		if cerr := q.getAccountByIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getAccountByIDStmt: %w", cerr)
		}
	}
	if q.getAccountPermissionsForCompanyStmt != nil {
		if cerr := q.getAccountPermissionsForCompanyStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getAccountPermissionsForCompanyStmt: %w", cerr)
		}
	}
	if q.getAccountRoleAssignmentsStmt != nil {
		if cerr := q.getAccountRoleAssignmentsStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getAccountRoleAssignmentsStmt: %w", cerr)
		}
	}
	if q.getAccountRolesForCompanyStmt != nil {
		if cerr := q.getAccountRolesForCompanyStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getAccountRolesForCompanyStmt: %w", cerr)
		}
	}
	if q.getAllPermissionsStmt != nil {
		if cerr := q.getAllPermissionsStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getAllPermissionsStmt: %w", cerr)
		}
	}
	if q.getAllRolesStmt != nil {
		if cerr := q.getAllRolesStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getAllRolesStmt: %w", cerr)
		}
	}
	if q.getPermissionByIDStmt != nil {
		if cerr := q.getPermissionByIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getPermissionByIDStmt: %w", cerr)
		}
	}
	if q.getPermissionByNameStmt != nil {
		if cerr := q.getPermissionByNameStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getPermissionByNameStmt: %w", cerr)
		}
	}
	if q.getPermissionsForRoleStmt != nil {
		if cerr := q.getPermissionsForRoleStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getPermissionsForRoleStmt: %w", cerr)
		}
	}
	if q.getRoleByIDStmt != nil {
		if cerr := q.getRoleByIDStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getRoleByIDStmt: %w", cerr)
		}
	}
	if q.getRoleByNameStmt != nil {
		if cerr := q.getRoleByNameStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing getRoleByNameStmt: %w", cerr)
		}
	}
	if q.removeRoleFromAccountStmt != nil {
		if cerr := q.removeRoleFromAccountStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing removeRoleFromAccountStmt: %w", cerr)
		}
	}
	if q.softDeleteAccountStmt != nil {
		if cerr := q.softDeleteAccountStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing softDeleteAccountStmt: %w", cerr)
		}
	}
	if q.updateAccountStmt != nil {
		if cerr := q.updateAccountStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateAccountStmt: %w", cerr)
		}
	}
	if q.updateAccountPasswordStmt != nil {
		if cerr := q.updateAccountPasswordStmt.Close(); cerr != nil {
			err = fmt.Errorf("error closing updateAccountPasswordStmt: %w", cerr)
		}
	}
	return err
}

func (q *Queries) exec(ctx context.Context, stmt *sql.Stmt, query string, args ...interface{}) (sql.Result, error) {
	switch {
	case stmt != nil && q.tx != nil:
		return q.tx.StmtContext(ctx, stmt).ExecContext(ctx, args...)
	case stmt != nil:
		return stmt.ExecContext(ctx, args...)
	default:
		return q.db.ExecContext(ctx, query, args...)
	}
}

func (q *Queries) query(ctx context.Context, stmt *sql.Stmt, query string, args ...interface{}) (*sql.Rows, error) {
	switch {
	case stmt != nil && q.tx != nil:
		return q.tx.StmtContext(ctx, stmt).QueryContext(ctx, args...)
	case stmt != nil:
		return stmt.QueryContext(ctx, args...)
	default:
		return q.db.QueryContext(ctx, query, args...)
	}
}

func (q *Queries) queryRow(ctx context.Context, stmt *sql.Stmt, query string, args ...interface{}) *sql.Row {
	switch {
	case stmt != nil && q.tx != nil:
		return q.tx.StmtContext(ctx, stmt).QueryRowContext(ctx, args...)
	case stmt != nil:
		return stmt.QueryRowContext(ctx, args...)
	default:
		return q.db.QueryRowContext(ctx, query, args...)
	}
}

type Queries struct {
	db                                  DBTX
	tx                                  *sql.Tx
	assignRoleToAccountStmt             *sql.Stmt
	checkAccountRoleInCompanyStmt       *sql.Stmt
	createCompanyAccountStmt            *sql.Stmt
	createUserAccountStmt               *sql.Stmt
	getAccountByEmailStmt               *sql.Stmt
	getAccountByIDStmt                  *sql.Stmt
	getAccountPermissionsForCompanyStmt *sql.Stmt
	getAccountRoleAssignmentsStmt       *sql.Stmt
	getAccountRolesForCompanyStmt       *sql.Stmt
	getAllPermissionsStmt               *sql.Stmt
	getAllRolesStmt                     *sql.Stmt
	getPermissionByIDStmt               *sql.Stmt
	getPermissionByNameStmt             *sql.Stmt
	getPermissionsForRoleStmt           *sql.Stmt
	getRoleByIDStmt                     *sql.Stmt
	getRoleByNameStmt                   *sql.Stmt
	removeRoleFromAccountStmt           *sql.Stmt
	softDeleteAccountStmt               *sql.Stmt
	updateAccountStmt                   *sql.Stmt
	updateAccountPasswordStmt           *sql.Stmt
}

func (q *Queries) WithTx(tx *sql.Tx) *Queries {
	return &Queries{
		db:                                  tx,
		tx:                                  tx,
		assignRoleToAccountStmt:             q.assignRoleToAccountStmt,
		checkAccountRoleInCompanyStmt:       q.checkAccountRoleInCompanyStmt,
		createCompanyAccountStmt:            q.createCompanyAccountStmt,
		createUserAccountStmt:               q.createUserAccountStmt,
		getAccountByEmailStmt:               q.getAccountByEmailStmt,
		getAccountByIDStmt:                  q.getAccountByIDStmt,
		getAccountPermissionsForCompanyStmt: q.getAccountPermissionsForCompanyStmt,
		getAccountRoleAssignmentsStmt:       q.getAccountRoleAssignmentsStmt,
		getAccountRolesForCompanyStmt:       q.getAccountRolesForCompanyStmt,
		getAllPermissionsStmt:               q.getAllPermissionsStmt,
		getAllRolesStmt:                     q.getAllRolesStmt,
		getPermissionByIDStmt:               q.getPermissionByIDStmt,
		getPermissionByNameStmt:             q.getPermissionByNameStmt,
		getPermissionsForRoleStmt:           q.getPermissionsForRoleStmt,
		getRoleByIDStmt:                     q.getRoleByIDStmt,
		getRoleByNameStmt:                   q.getRoleByNameStmt,
		removeRoleFromAccountStmt:           q.removeRoleFromAccountStmt,
		softDeleteAccountStmt:               q.softDeleteAccountStmt,
		updateAccountStmt:                   q.updateAccountStmt,
		updateAccountPasswordStmt:           q.updateAccountPasswordStmt,
	}
}

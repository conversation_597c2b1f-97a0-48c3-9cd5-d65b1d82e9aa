// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package db

import (
	"database/sql"
	"time"

	"github.com/google/uuid"
	"github.com/sqlc-dev/pqtype"
)

type Account struct {
	ID           uuid.UUID    `json:"id"`
	Name         string       `json:"name"`
	Email        string       `json:"email"`
	Type         string       `json:"type"`
	Balance      int64        `json:"balance"`
	CurrencyCode string       `json:"currency_code"`
	IsActive     bool         `json:"is_active"`
	CreatedAt    time.Time    `json:"created_at"`
	UpdatedAt    time.Time    `json:"updated_at"`
	DeletedAt    sql.NullTime `json:"deleted_at"`
}

type LedgerEntry struct {
	ID            uuid.UUID      `json:"id"`
	TransactionID uuid.UUID      `json:"transaction_id"`
	AccountID     uuid.UUID      `json:"account_id"`
	Type          string         `json:"type"`
	Amount        int64          `json:"amount"`
	CurrencyCode  string         `json:"currency_code"`
	BalanceAfter  int64          `json:"balance_after"`
	Description   sql.NullString `json:"description"`
	CreatedAt     time.Time      `json:"created_at"`
}

type LedgerTransaction struct {
	ID           uuid.UUID             `json:"id"`
	ReferenceID  string                `json:"reference_id"`
	Type         string                `json:"type"`
	Status       string                `json:"status"`
	Amount       int64                 `json:"amount"`
	CurrencyCode string                `json:"currency_code"`
	Description  sql.NullString        `json:"description"`
	Metadata     pqtype.NullRawMessage `json:"metadata"`
	CreatedAt    time.Time             `json:"created_at"`
	UpdatedAt    time.Time             `json:"updated_at"`
}

type PaymentMethod struct {
	ID          uuid.UUID             `json:"id"`
	AccountID   uuid.UUID             `json:"account_id"`
	Type        string                `json:"type"`
	Provider    string                `json:"provider"`
	LastFour    sql.NullString        `json:"last_four"`
	ExpiryMonth sql.NullInt32         `json:"expiry_month"`
	ExpiryYear  sql.NullInt32         `json:"expiry_year"`
	IsDefault   bool                  `json:"is_default"`
	IsActive    bool                  `json:"is_active"`
	Metadata    pqtype.NullRawMessage `json:"metadata"`
	CreatedAt   time.Time             `json:"created_at"`
	UpdatedAt   time.Time             `json:"updated_at"`
	DeletedAt   sql.NullTime          `json:"deleted_at"`
}

// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package db

import (
	"github.com/jackc/pgx/v5/pgtype"
)

type Account struct {
	ID           pgtype.UUID        `json:"id"`
	Name         string             `json:"name"`
	Email        string             `json:"email"`
	Type         string             `json:"type"`
	Balance      int64              `json:"balance"`
	CurrencyCode string             `json:"currency_code"`
	IsActive     bool               `json:"is_active"`
	CreatedAt    pgtype.Timestamptz `json:"created_at"`
	UpdatedAt    pgtype.Timestamptz `json:"updated_at"`
	DeletedAt    pgtype.Timestamptz `json:"deleted_at"`
	PasswordHash pgtype.Text        `json:"password_hash"`
}

type AccountRole struct {
	AccountID pgtype.UUID        `json:"account_id"`
	RoleID    pgtype.UUID        `json:"role_id"`
	CompanyID pgtype.UUID        `json:"company_id"`
	CreatedAt pgtype.Timestamptz `json:"created_at"`
}

type LedgerEntry struct {
	ID            pgtype.UUID        `json:"id"`
	TransactionID pgtype.UUID        `json:"transaction_id"`
	AccountID     pgtype.UUID        `json:"account_id"`
	Type          string             `json:"type"`
	Amount        int64              `json:"amount"`
	CurrencyCode  string             `json:"currency_code"`
	BalanceAfter  int64              `json:"balance_after"`
	Description   pgtype.Text        `json:"description"`
	CreatedAt     pgtype.Timestamptz `json:"created_at"`
}

type LedgerTransaction struct {
	ID           pgtype.UUID        `json:"id"`
	ReferenceID  string             `json:"reference_id"`
	Type         string             `json:"type"`
	Status       string             `json:"status"`
	Amount       int64              `json:"amount"`
	CurrencyCode string             `json:"currency_code"`
	Description  pgtype.Text        `json:"description"`
	Metadata     []byte             `json:"metadata"`
	CreatedAt    pgtype.Timestamptz `json:"created_at"`
	UpdatedAt    pgtype.Timestamptz `json:"updated_at"`
}

type PaymentMethod struct {
	ID          pgtype.UUID        `json:"id"`
	AccountID   pgtype.UUID        `json:"account_id"`
	Type        string             `json:"type"`
	Provider    string             `json:"provider"`
	LastFour    pgtype.Text        `json:"last_four"`
	ExpiryMonth pgtype.Int4        `json:"expiry_month"`
	ExpiryYear  pgtype.Int4        `json:"expiry_year"`
	IsDefault   bool               `json:"is_default"`
	IsActive    bool               `json:"is_active"`
	Metadata    []byte             `json:"metadata"`
	CreatedAt   pgtype.Timestamptz `json:"created_at"`
	UpdatedAt   pgtype.Timestamptz `json:"updated_at"`
	DeletedAt   pgtype.Timestamptz `json:"deleted_at"`
}

type Permission struct {
	ID          pgtype.UUID        `json:"id"`
	Name        string             `json:"name"`
	Description pgtype.Text        `json:"description"`
	CreatedAt   pgtype.Timestamptz `json:"created_at"`
	UpdatedAt   pgtype.Timestamptz `json:"updated_at"`
}

type Role struct {
	ID          pgtype.UUID        `json:"id"`
	Name        string             `json:"name"`
	Description pgtype.Text        `json:"description"`
	CreatedAt   pgtype.Timestamptz `json:"created_at"`
	UpdatedAt   pgtype.Timestamptz `json:"updated_at"`
}

type RolePermission struct {
	RoleID       pgtype.UUID `json:"role_id"`
	PermissionID pgtype.UUID `json:"permission_id"`
}

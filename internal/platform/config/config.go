package config

import (
	"fmt"
	"strings"
	"time"

	"github.com/knadh/koanf/parsers/yaml"
	"github.com/knadh/koanf/providers/env"
	"github.com/knadh/koanf/providers/file"
	"github.com/knadh/koanf/v2"
)

// Config represents the application configuration
type Config struct {
	Server   ServerConfig   `koanf:"server"`
	Logger   LoggerConfig   `koanf:"logger"`
	Database DatabaseConfig `koanf:"database"`
	Redis    RedisConfig    `koanf:"redis"`
	Sentry   SentryConfig   `koanf:"sentry"`
}

// ServerConfig holds server-related configuration
type ServerConfig struct {
	Host         string        `koanf:"host"`
	Port         int           `koanf:"port"`
	Mode         string        `koanf:"mode"`
	ReadTimeout  time.Duration `koanf:"read_timeout"`
	WriteTimeout time.Duration `koanf:"write_timeout"`
	IdleTimeout  time.Duration `koanf:"idle_timeout"`
}

// LoggerConfig holds logging configuration
type LoggerConfig struct {
	Level string `koanf:"level"`
	Mode  string `koanf:"mode"`
}

// DatabaseConfig holds database connection configuration
type DatabaseConfig struct {
	Host            string        `koanf:"host"`
	Port            int           `koanf:"port"`
	User            string        `koanf:"user"`
	Password        string        `koanf:"password"`
	Name            string        `koanf:"name"`
	SSLMode         string        `koanf:"ssl_mode"`
	MaxOpenConns    int           `koanf:"max_open_conns"`
	MaxIdleConns    int           `koanf:"max_idle_conns"`
	ConnMaxLifetime time.Duration `koanf:"conn_max_lifetime"`
}

// RedisConfig holds Redis connection configuration
type RedisConfig struct {
	Host         string `koanf:"host"`
	Port         int    `koanf:"port"`
	Password     string `koanf:"password"`
	DB           int    `koanf:"db"`
	MaxRetries   int    `koanf:"max_retries"`
	PoolSize     int    `koanf:"pool_size"`
	MinIdleConns int    `koanf:"min_idle_conns"`
}

// SentryConfig holds Sentry configuration
type SentryConfig struct {
	DSN              string  `koanf:"dsn"`
	Environment      string  `koanf:"environment"`
	TracesSampleRate float64 `koanf:"traces_sample_rate"`
}

// New creates a new configuration instance by loading from config file and environment variables
func New() (*Config, error) {
	k := koanf.New(".")

	// Load from config file
	if err := k.Load(file.Provider("configs/config.yaml"), yaml.Parser()); err != nil {
		return nil, fmt.Errorf("failed to load config file: %w", err)
	}

	// Load environment variables with prefix
	if err := k.Load(env.Provider("PAYMENT_", ".", func(s string) string {
		// Convert PAYMENT_DATABASE_HOST to database.host
		return strings.ToLower(strings.Replace(strings.TrimPrefix(s, "PAYMENT_"), "_", ".", -1))
	}), nil); err != nil {
		return nil, fmt.Errorf("failed to load environment variables: %w", err)
	}

	var cfg Config
	if err := k.Unmarshal("", &cfg); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &cfg, nil
}

// GetDatabaseDSN returns the database connection string
func (c *DatabaseConfig) GetDatabaseDSN() string {
	return fmt.Sprintf("postgres://%s:%s@%s:%d/%s?sslmode=%s",
		c.User, c.Password, c.Host, c.Port, c.Name, c.SSLMode)
}

// GetServerAddress returns the server address
func (c *ServerConfig) GetServerAddress() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

// GetRedisAddress returns the Redis address
func (c *RedisConfig) GetRedisAddress() string {
	return fmt.Sprintf("%s:%d", c.Host, c.Port)
}

package middleware

import (
	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// TracingMiddleware generates a unique trace ID for each request and adds it to the context
// Rule 7.1: TracingMiddleware MUST be implemented and placed first in the Gin middleware chain
func TracingMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Generate a unique trace ID
		traceID := uuid.New().String()

		// Add trace ID to the Gin context
		c.Set("trace_id", traceID)

		// Add trace ID to response headers for debugging
		c.<PERSON>er("X-Trace-ID", traceID)

		// Continue to next middleware
		c.Next()
	}
}

// GetTraceID retrieves the trace ID from the Gin context
func GetTraceID(c *gin.Context) string {
	if traceID, exists := c.Get("trace_id"); exists {
		if id, ok := traceID.(string); ok {
			return id
		}
	}
	return "unknown"
}

package middleware

import (
	"time"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

// LoggerMiddleware logs incoming requests and outgoing responses
// Rule 7.2: LoggerMiddleware MUST be implemented and log request/response details with trace_id
// Rule 5.1: All logging MUST be done using the global zerolog instance
// Rule 5.2: Every log statement MUST be structured
// Rule 5.3: Every log statement within HTTP request context MUST include trace_id
func LoggerMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Record start time
		startTime := time.Now()
		
		// Get trace ID from context
		traceID := GetTraceID(c)
		
		// Log incoming request
		log.Info().
			Str("trace_id", traceID).
			Str("method", c.Request.Method).
			Str("path", c.Request.URL.Path).
			Str("query", c.Request.URL.RawQuery).
			Str("user_agent", c.Request.UserAgent()).
			Str("remote_addr", c.ClientIP()).
			Msg("incoming request")
		
		// Process request
		c.Next()
		
		// Calculate latency
		latency := time.Since(startTime)
		
		// Get response status
		status := c.Writer.Status()
		
		// Determine log level based on status code
		logEvent := log.Info()
		if status >= 400 && status < 500 {
			logEvent = log.Warn()
		} else if status >= 500 {
			logEvent = log.Error()
		}
		
		// Log outgoing response
		logEvent.
			Str("trace_id", traceID).
			Str("method", c.Request.Method).
			Str("path", c.Request.URL.Path).
			Int("status", status).
			Dur("latency", latency).
			Int("response_size", c.Writer.Size()).
			Msg("outgoing response")
	}
}

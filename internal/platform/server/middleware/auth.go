package middleware

import (
	"errors"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"

	"github.com/wongpinter/payment/internal/account"
)

// AuthMiddleware validates JWT tokens and adds user information to context
func AuthMiddleware(authService *account.AuthService) gin.HandlerFunc {
	return func(c *gin.Context) {
		traceID := GetTraceID(c)

		// Get Authorization header
		authHeader := c.Get<PERSON>eader("Authorization")
		if authHeader == "" {
			log.Warn().
				Str("trace_id", traceID).
				Msg("missing authorization header")
			AbortWithError(c, errors.New("authorization header required"))
			return
		}

		// Check Bearer token format
		parts := strings.SplitN(authHeader, " ", 2)
		if len(parts) != 2 || parts[0] != "Bearer" {
			log.Warn().
				Str("trace_id", traceID).
				Msg("invalid authorization header format")
			AbortWithError(c, errors.New("invalid authorization header format"))
			return
		}

		tokenString := parts[1]

		// Validate the JWT token
		claims, err := authService.ValidateAccessToken(c.Request.Context(), tokenString)
		if err != nil {
			log.Warn().
				Str("trace_id", traceID).
				Err(err).
				Msg("invalid access token")
			AbortWithError(c, account.ErrInvalidCredentials)
			return
		}

		// Add user information to context
		c.Set("user_id", claims.UserID)
		c.Set("company_id", claims.CompanyID)
		c.Set("roles", claims.Roles)

		log.Debug().
			Str("trace_id", traceID).
			Str("user_id", claims.UserID).
			Str("company_id", claims.CompanyID).
			Strs("roles", claims.Roles).
			Msg("user authenticated")

		// Continue to next middleware/handler
		c.Next()
	}
}

// RequireRole middleware ensures the user has at least one of the specified roles
func RequireRole(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		traceID := GetTraceID(c)
		userRoles, exists := c.Get("roles")
		if !exists {
			log.Warn().
				Str("trace_id", traceID).
				Msg("no roles found in context")
			AbortWithError(c, errors.New("insufficient permissions"))
			return
		}

		userRoleSlice, ok := userRoles.([]string)
		if !ok {
			log.Warn().
				Str("trace_id", traceID).
				Msg("invalid roles format in context")
			AbortWithError(c, errors.New("insufficient permissions"))
			return
		}

		// Check if user has any of the required roles
		hasRole := false
		for _, requiredRole := range roles {
			for _, userRole := range userRoleSlice {
				if userRole == requiredRole {
					hasRole = true
					break
				}
			}
			if hasRole {
				break
			}
		}

		if !hasRole {
			log.Warn().
				Str("trace_id", traceID).
				Strs("user_roles", userRoleSlice).
				Strs("required_roles", roles).
				Msg("insufficient role permissions")
			AbortWithError(c, errors.New("insufficient permissions"))
			return
		}

		c.Next()
	}
}

// RequireCompany middleware ensures the company_id in the URL matches the user's context
func RequireCompany() gin.HandlerFunc {
	return func(c *gin.Context) {
		traceID := GetTraceID(c)

		// Get company ID from URL parameter
		urlCompanyID := c.Param("company_id")
		if urlCompanyID == "" {
			// If no company_id in URL, continue (not all endpoints require it)
			c.Next()
			return
		}

		// Get company ID from JWT claims
		contextCompanyID, exists := c.Get("company_id")
		if !exists {
			log.Warn().
				Str("trace_id", traceID).
				Msg("no company_id found in token")
			AbortWithError(c, errors.New("insufficient permissions"))
			return
		}

		contextCompanyIDStr, ok := contextCompanyID.(string)
		if !ok {
			log.Warn().
				Str("trace_id", traceID).
				Msg("invalid company_id format in token")
			AbortWithError(c, errors.New("insufficient permissions"))
			return
		}

		// Verify company IDs match
		if urlCompanyID != contextCompanyIDStr {
			log.Warn().
				Str("trace_id", traceID).
				Str("url_company_id", urlCompanyID).
				Str("token_company_id", contextCompanyIDStr).
				Msg("company_id mismatch")
			AbortWithError(c, errors.New("insufficient permissions"))
			return
		}

		c.Next()
	}
}

// GetUserID retrieves the user ID from the Gin context
func GetUserID(c *gin.Context) string {
	if userID, exists := c.Get("user_id"); exists {
		if id, ok := userID.(string); ok {
			return id
		}
	}
	return ""
}

// GetCompanyID retrieves the company ID from the Gin context
func GetCompanyID(c *gin.Context) string {
	if companyID, exists := c.Get("company_id"); exists {
		if id, ok := companyID.(string); ok {
			return id
		}
	}
	return ""
}

// GetUserRoles retrieves the user roles from the Gin context
func GetUserRoles(c *gin.Context) []string {
	if roles, exists := c.Get("roles"); exists {
		if roleSlice, ok := roles.([]string); ok {
			return roleSlice
		}
	}
	return []string{}
}

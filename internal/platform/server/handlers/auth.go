package handlers

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"

	"github.com/wongpinter/payment/internal/account"
	"github.com/wongpinter/payment/internal/platform/server/middleware"
	"github.com/wongpinter/payment/internal/platform/webapi"
)

// AuthHandler handles authentication-related HTTP requests
type AuthHandler struct {
	authService *account.AuthService
}

// NewAuthHandler creates a new authentication handler
func <PERSON><PERSON>andler(authService *account.AuthService) *AuthHandler {
	return &AuthHandler{
		authService: authService,
	}
}

// Register<PERSON>ser handles user registration requests
// Rule 4.1: All successful API responses MUST use webapi.Success() helper
// Rule 4.2: Client-facing error responses (4xx) MUST use webapi.Error() helper
// Rule 3.3: API handlers MUST NOT handle errors directly, errors handled by centralized middleware
func (h *<PERSON>th<PERSON>and<PERSON>) RegisterUser(c *gin.Context) {
	traceID := middleware.GetTraceID(c)

	var req account.RegisterUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Warn().
			Str("trace_id", traceID).
			Err(err).
			Msg("invalid registration request")
		middleware.AbortWithError(c, err)
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("email", req.Email).
		Str("name", req.Name).
		Msg("user registration attempt")

	user, err := h.authService.RegisterUser(c.Request.Context(), req)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("user_id", user.ID.String()).
		Str("email", user.Email).
		Msg("user registered successfully")

	// Return user data without password hash
	response := map[string]interface{}{
		"id":            user.ID,
		"name":          user.Name,
		"email":         user.Email,
		"type":          user.Type,
		"balance":       user.Balance,
		"currency_code": user.CurrencyCode,
		"is_active":     user.IsActive,
		"created_at":    user.CreatedAt,
		"updated_at":    user.UpdatedAt,
	}

	webapi.Success(c, http.StatusCreated, "User registered successfully", response)
}

// Login handles user login requests
func (h *AuthHandler) Login(c *gin.Context) {
	traceID := middleware.GetTraceID(c)

	var req account.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		log.Warn().
			Str("trace_id", traceID).
			Err(err).
			Msg("invalid login request")
		middleware.AbortWithError(c, err)
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("email", req.Email).
		Msg("user login attempt")

	tokenResponse, err := h.authService.Login(c.Request.Context(), req)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("email", req.Email).
		Msg("user logged in successfully")

	webapi.Success(c, http.StatusOK, tokenResponse)
}

// RefreshToken handles token refresh requests
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	traceID := middleware.GetTraceID(c)

	var req struct {
		RefreshToken string `json:"refresh_token" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		log.Warn().
			Str("trace_id", traceID).
			Err(err).
			Msg("invalid refresh token request")
		middleware.AbortWithError(c, err)
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Msg("token refresh attempt")

	tokenResponse, err := h.authService.RefreshToken(c.Request.Context(), req.RefreshToken)
	if err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Msg("token refreshed successfully")

	webapi.Success(c, http.StatusOK, tokenResponse)
}

// Logout handles user logout requests
func (h *AuthHandler) Logout(c *gin.Context) {
	traceID := middleware.GetTraceID(c)
	userIDStr := middleware.GetUserID(c)

	if userIDStr == "" {
		log.Warn().
			Str("trace_id", traceID).
			Msg("no user_id in context for logout")
		middleware.AbortWithError(c, account.ErrInvalidCredentials)
		return
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		log.Warn().
			Str("trace_id", traceID).
			Str("user_id", userIDStr).
			Err(err).
			Msg("invalid user_id format")
		middleware.AbortWithError(c, account.ErrInvalidCredentials)
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("user_id", userIDStr).
		Msg("user logout attempt")

	if err := h.authService.Logout(c.Request.Context(), userID); err != nil {
		middleware.AbortWithError(c, err)
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("user_id", userIDStr).
		Msg("user logged out successfully")

	webapi.Success(c, http.StatusOK, "Logout successful", nil)
}

// GetProfile returns the current user's profile information
func (h *AuthHandler) GetProfile(c *gin.Context) {
	traceID := middleware.GetTraceID(c)
	userIDStr := middleware.GetUserID(c)

	if userIDStr == "" {
		log.Warn().
			Str("trace_id", traceID).
			Msg("no user_id in context for profile")
		middleware.AbortWithError(c, account.ErrInvalidCredentials)
		return
	}

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		log.Warn().
			Str("trace_id", traceID).
			Str("user_id", userIDStr).
			Err(err).
			Msg("invalid user_id format")
		middleware.AbortWithError(c, account.ErrInvalidCredentials)
		return
	}

	log.Info().
		Str("trace_id", traceID).
		Str("user_id", userIDStr).
		Msg("get profile request")

	// Note: This would typically use a repository method to get user by ID
	// For now, we'll return basic information from the JWT claims
	response := map[string]interface{}{
		"user_id":    userIDStr,
		"company_id": middleware.GetCompanyID(c),
		"roles":      middleware.GetUserRoles(c),
	}

	webapi.Success(c, http.StatusOK, "Profile retrieved successfully", response)
}

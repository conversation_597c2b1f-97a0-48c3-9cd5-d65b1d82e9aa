package handlers_test

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/wongpinter/payment/internal/account"
	"github.com/wongpinter/payment/internal/persistence/postgres"
	"github.com/wongpinter/payment/internal/platform/server/handlers"
	"github.com/wongpinter/payment/internal/platform/server/middleware"
	"github.com/wongpinter/payment/internal/platform/testhelpers"
	"github.com/wongpinter/payment/internal/platform/webapi"
)

// setupTestRouter creates a test router with all middleware and handlers
func setupTestRouter(authService *account.AuthService) *gin.Engine {
	gin.SetMode(gin.TestMode)

	router := gin.New()

	// Add middleware in correct order (Rules 7.1, 7.2, 7.3)
	router.Use(middleware.TracingMiddleware())
	router.Use(middleware.LoggerMiddleware())
	router.Use(gin.Recovery())
	// Rule 7.3: ErrorMiddleware MUST be last in the chain
	router.Use(middleware.ErrorMiddleware())

	// Initialize handlers
	authHandler := handlers.NewAuthHandler(authService)

	// API routes group
	api := router.Group("/api/v1")
	{
		// Authentication routes (public)
		auth := api.Group("/auth")
		{
			auth.POST("/register", authHandler.RegisterUser)
			auth.POST("/login", authHandler.Login)
			auth.POST("/refresh", authHandler.RefreshToken)
		}

		// Protected routes
		protected := api.Group("/")
		protected.Use(middleware.AuthMiddleware(authService))
		{
			protected.POST("/auth/logout", authHandler.Logout)
			protected.GET("/auth/profile", authHandler.GetProfile)
		}
	}

	return router
}

func TestAuthHandler_RegisterUser(t *testing.T) {
	ctx := context.Background()

	// Setup test database and Redis
	pool, dbCleanup, err := testhelpers.SetupTestDatabase(ctx)
	require.NoError(t, err, "Failed to setup test database")
	defer dbCleanup()

	redisClient, redisCleanup, err := testhelpers.SetupTestRedis(ctx)
	require.NoError(t, err, "Failed to setup test Redis")
	defer redisCleanup()

	// Create repository and service
	repo := postgres.NewAccountRepository(pool)
	authService := account.NewAuthService(
		repo,
		redisClient,
		"test-jwt-secret",
		15*time.Minute, // access token TTL
		24*time.Hour,   // refresh token TTL
		"test-issuer",
	)

	// Setup router
	router := setupTestRouter(authService)

	t.Run("successful registration", func(t *testing.T) {
		reqBody := map[string]interface{}{
			"name":     "John Doe",
			"email":    "<EMAIL>",
			"password": "securepassword123",
		}

		jsonBody, err := json.Marshal(reqBody)
		require.NoError(t, err)

		req := httptest.NewRequest(http.MethodPost, "/api/v1/auth/register", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verify response
		assert.Equal(t, http.StatusCreated, w.Code, "Should return 201 Created")

		var response webapi.SuccessResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err, "Should be valid JSON")

		assert.Equal(t, "success", response.Status, "Response should be successful")
		assert.Equal(t, "User registered successfully", response.Data.(map[string]interface{})["message"])

		// Verify user data in response
		userData := response.Data.(map[string]interface{})["user"].(map[string]interface{})
		assert.Equal(t, "John Doe", userData["name"])
		assert.Equal(t, "<EMAIL>", userData["email"])
		assert.Equal(t, "personal", userData["type"])
		assert.Equal(t, true, userData["is_active"])
		assert.NotNil(t, userData["id"])
		assert.NotNil(t, userData["created_at"])
		assert.NotNil(t, userData["updated_at"])

		// Verify password hash is not included
		assert.Nil(t, userData["password_hash"])
	})

	t.Run("duplicate email error", func(t *testing.T) {
		// First registration
		reqBody := map[string]interface{}{
			"name":     "Jane Doe",
			"email":    "<EMAIL>",
			"password": "securepassword123",
		}

		jsonBody, err := json.Marshal(reqBody)
		require.NoError(t, err)

		req := httptest.NewRequest(http.MethodPost, "/api/v1/auth/register", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)
		assert.Equal(t, http.StatusCreated, w.Code, "First registration should succeed")

		// Second registration with same email should fail
		jsonBody2, err := json.Marshal(reqBody)
		require.NoError(t, err)

		req2 := httptest.NewRequest(http.MethodPost, "/api/v1/auth/register", bytes.NewBuffer(jsonBody2))
		req2.Header.Set("Content-Type", "application/json")

		w2 := httptest.NewRecorder()
		router.ServeHTTP(w2, req2)

		// Verify error response
		assert.Equal(t, http.StatusConflict, w2.Code, "Should return 409 Conflict")

		if w2.Body.Len() > 0 {
			var response webapi.ErrorResponse
			err = json.Unmarshal(w2.Body.Bytes(), &response)
			require.NoError(t, err, "Should be valid JSON")

			assert.Equal(t, "error", response.Status, "Response should indicate failure")
			assert.Contains(t, response.Error.Message, "already exists")
		}
	})

	t.Run("invalid request body", func(t *testing.T) {
		reqBody := map[string]interface{}{
			"name":     "Bob Wilson",
			"email":    "invalid-email", // Invalid email format
			"password": "short",         // Too short password
		}

		jsonBody, err := json.Marshal(reqBody)
		require.NoError(t, err)

		req := httptest.NewRequest(http.MethodPost, "/api/v1/auth/register", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verify error response
		assert.Equal(t, http.StatusBadRequest, w.Code, "Should return 400 Bad Request")

		var response webapi.ErrorResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err, "Should be valid JSON")

		assert.Equal(t, "error", response.Status, "Response should indicate failure")
	})

	t.Run("malformed JSON", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodPost, "/api/v1/auth/register", bytes.NewBufferString("{invalid json"))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verify error response
		assert.Equal(t, http.StatusBadRequest, w.Code, "Should return 400 Bad Request")

		var response webapi.ErrorResponse
		err := json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err, "Should be valid JSON")

		assert.Equal(t, "error", response.Status, "Response should indicate failure")
	})
}

func TestAuthHandler_Login(t *testing.T) {
	ctx := context.Background()

	// Setup test database and Redis
	pool, dbCleanup, err := testhelpers.SetupTestDatabase(ctx)
	require.NoError(t, err, "Failed to setup test database")
	defer dbCleanup()

	redisClient, redisCleanup, err := testhelpers.SetupTestRedis(ctx)
	require.NoError(t, err, "Failed to setup test Redis")
	defer redisCleanup()

	// Create repository and service
	repo := postgres.NewAccountRepository(pool)
	authService := account.NewAuthService(
		repo,
		redisClient,
		"test-jwt-secret",
		15*time.Minute, // access token TTL
		24*time.Hour,   // refresh token TTL
		"test-issuer",
	)

	// Setup router
	router := setupTestRouter(authService)

	// Register a test user first
	registerReq := account.RegisterUserRequest{
		Name:     "Test User",
		Email:    "<EMAIL>",
		Password: "securepassword123",
	}
	registeredUser, err := authService.RegisterUser(ctx, registerReq)
	require.NoError(t, err, "Failed to register test user")
	require.NotNil(t, registeredUser, "Registered user should not be nil")
	t.Logf("Registered user: %+v", registeredUser)

	t.Run("successful login", func(t *testing.T) {
		reqBody := map[string]interface{}{
			"email":    "<EMAIL>",
			"password": "securepassword123",
		}

		jsonBody, err := json.Marshal(reqBody)
		require.NoError(t, err)

		req := httptest.NewRequest(http.MethodPost, "/api/v1/auth/login", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verify response
		assert.Equal(t, http.StatusOK, w.Code, "Should return 200 OK")

		var response webapi.SuccessResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err, "Should be valid JSON")

		assert.Equal(t, "success", response.Status, "Response should be successful")

		// Verify token response structure
		tokenData := response.Data.(map[string]interface{})
		assert.NotEmpty(t, tokenData["access_token"], "Should have access token")
		assert.NotEmpty(t, tokenData["refresh_token"], "Should have refresh token")
		assert.Equal(t, "Bearer", tokenData["token_type"])
		assert.NotNil(t, tokenData["expires_in"])
	})

	t.Run("invalid credentials", func(t *testing.T) {
		reqBody := map[string]interface{}{
			"email":    "<EMAIL>",
			"password": "wrongpassword",
		}

		jsonBody, err := json.Marshal(reqBody)
		require.NoError(t, err)

		req := httptest.NewRequest(http.MethodPost, "/api/v1/auth/login", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verify error response
		assert.Equal(t, http.StatusUnauthorized, w.Code, "Should return 401 Unauthorized")

		var response webapi.ErrorResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err, "Should be valid JSON")

		assert.Equal(t, "error", response.Status, "Response should indicate failure")
		assert.Contains(t, response.Error.Message, "invalid email or password")
	})

	t.Run("non-existent user", func(t *testing.T) {
		reqBody := map[string]interface{}{
			"email":    "<EMAIL>",
			"password": "anypassword",
		}

		jsonBody, err := json.Marshal(reqBody)
		require.NoError(t, err)

		req := httptest.NewRequest(http.MethodPost, "/api/v1/auth/login", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verify error response
		assert.Equal(t, http.StatusUnauthorized, w.Code, "Should return 401 Unauthorized")

		var response webapi.ErrorResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err, "Should be valid JSON")

		assert.Equal(t, "error", response.Status, "Response should indicate failure")
		assert.Contains(t, response.Error.Message, "invalid email or password")
	})

	t.Run("missing fields", func(t *testing.T) {
		reqBody := map[string]interface{}{
			"email": "<EMAIL>",
			// Missing password
		}

		jsonBody, err := json.Marshal(reqBody)
		require.NoError(t, err)

		req := httptest.NewRequest(http.MethodPost, "/api/v1/auth/login", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verify error response
		assert.Equal(t, http.StatusBadRequest, w.Code, "Should return 400 Bad Request")

		var response webapi.ErrorResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err, "Should be valid JSON")

		assert.Equal(t, "error", response.Status, "Response should indicate failure")
	})
}

func TestAuthHandler_RefreshToken(t *testing.T) {
	ctx := context.Background()

	// Setup test database and Redis
	pool, dbCleanup, err := testhelpers.SetupTestDatabase(ctx)
	require.NoError(t, err, "Failed to setup test database")
	defer dbCleanup()

	redisClient, redisCleanup, err := testhelpers.SetupTestRedis(ctx)
	require.NoError(t, err, "Failed to setup test Redis")
	defer redisCleanup()

	// Create repository and service
	repo := postgres.NewAccountRepository(pool)
	authService := account.NewAuthService(
		repo,
		redisClient,
		"test-jwt-secret",
		15*time.Minute, // access token TTL
		24*time.Hour,   // refresh token TTL
		"test-issuer",
	)

	// Setup router
	router := setupTestRouter(authService)

	// Register and login to get a refresh token
	registerReq := account.RegisterUserRequest{
		Name:     "Test User",
		Email:    "<EMAIL>",
		Password: "securepassword123",
	}
	_, err = authService.RegisterUser(ctx, registerReq)
	require.NoError(t, err, "Failed to register test user")

	loginReq := account.LoginRequest{
		Email:    "<EMAIL>",
		Password: "securepassword123",
	}
	tokenResponse, err := authService.Login(ctx, loginReq)
	require.NoError(t, err, "Failed to login test user")

	t.Run("successful refresh", func(t *testing.T) {
		reqBody := map[string]interface{}{
			"refresh_token": tokenResponse.RefreshToken,
		}

		jsonBody, err := json.Marshal(reqBody)
		require.NoError(t, err)

		req := httptest.NewRequest(http.MethodPost, "/api/v1/auth/refresh", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verify response
		assert.Equal(t, http.StatusOK, w.Code, "Should return 200 OK")

		var response webapi.SuccessResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err, "Should be valid JSON")

		assert.Equal(t, "success", response.Status, "Response should be successful")

		// Verify new token response structure
		newTokenData := response.Data.(map[string]interface{})
		assert.NotEmpty(t, newTokenData["access_token"], "Should have new access token")
		assert.NotEmpty(t, newTokenData["refresh_token"], "Should have new refresh token")
		assert.Equal(t, "Bearer", newTokenData["token_type"])
		assert.NotNil(t, newTokenData["expires_in"])

		// Verify tokens are different from original
		assert.NotEqual(t, tokenResponse.AccessToken, newTokenData["access_token"], "Access token should be different")
		assert.NotEqual(t, tokenResponse.RefreshToken, newTokenData["refresh_token"], "Refresh token should be different")
	})

	t.Run("invalid refresh token", func(t *testing.T) {
		reqBody := map[string]interface{}{
			"refresh_token": "invalid-refresh-token",
		}

		jsonBody, err := json.Marshal(reqBody)
		require.NoError(t, err)

		req := httptest.NewRequest(http.MethodPost, "/api/v1/auth/refresh", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verify error response
		assert.Equal(t, http.StatusUnauthorized, w.Code, "Should return 401 Unauthorized")

		var response webapi.ErrorResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err, "Should be valid JSON")

		assert.Equal(t, "error", response.Status, "Response should indicate failure")
		assert.Contains(t, response.Error.Message, "invalid")
	})

	t.Run("missing refresh token", func(t *testing.T) {
		reqBody := map[string]interface{}{
			// Missing refresh_token field
		}

		jsonBody, err := json.Marshal(reqBody)
		require.NoError(t, err)

		req := httptest.NewRequest(http.MethodPost, "/api/v1/auth/refresh", bytes.NewBuffer(jsonBody))
		req.Header.Set("Content-Type", "application/json")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verify error response
		assert.Equal(t, http.StatusBadRequest, w.Code, "Should return 400 Bad Request")

		var response webapi.ErrorResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err, "Should be valid JSON")

		assert.Equal(t, "error", response.Status, "Response should indicate failure")
	})
}

func TestAuthHandler_ProtectedEndpoints(t *testing.T) {
	ctx := context.Background()

	// Setup test database and Redis
	pool, dbCleanup, err := testhelpers.SetupTestDatabase(ctx)
	require.NoError(t, err, "Failed to setup test database")
	defer dbCleanup()

	redisClient, redisCleanup, err := testhelpers.SetupTestRedis(ctx)
	require.NoError(t, err, "Failed to setup test Redis")
	defer redisCleanup()

	// Create repository and service
	repo := postgres.NewAccountRepository(pool)
	authService := account.NewAuthService(
		repo,
		redisClient,
		"test-jwt-secret",
		15*time.Minute, // access token TTL
		24*time.Hour,   // refresh token TTL
		"test-issuer",
	)

	// Setup router
	router := setupTestRouter(authService)

	// Register and login to get access token
	registerReq := account.RegisterUserRequest{
		Name:     "Test User",
		Email:    "<EMAIL>",
		Password: "securepassword123",
	}
	_, err = authService.RegisterUser(ctx, registerReq)
	require.NoError(t, err, "Failed to register test user")

	loginReq := account.LoginRequest{
		Email:    "<EMAIL>",
		Password: "securepassword123",
	}
	tokenResponse, err := authService.Login(ctx, loginReq)
	require.NoError(t, err, "Failed to login test user")

	t.Run("get profile with valid token", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/api/v1/auth/profile", nil)
		req.Header.Set("Authorization", "Bearer "+tokenResponse.AccessToken)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verify response
		assert.Equal(t, http.StatusOK, w.Code, "Should return 200 OK")

		var response webapi.SuccessResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err, "Should be valid JSON")

		assert.Equal(t, "success", response.Status, "Response should be successful")

		// Verify profile data
		profileData := response.Data.(map[string]interface{})
		assert.Equal(t, "Profile retrieved successfully", profileData["message"])

		profile := profileData["profile"].(map[string]interface{})
		assert.NotEmpty(t, profile["user_id"], "Should have user_id")
		assert.NotNil(t, profile["company_id"], "Should have company_id")
		// Verify roles are included (can be empty array)
		roles, exists := profile["roles"]
		assert.True(t, exists, "Should have roles field")
		// Roles can be nil or empty array, just check it exists
		_ = roles
	})

	t.Run("get profile without token", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/api/v1/auth/profile", nil)
		// No Authorization header

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verify error response
		assert.Equal(t, http.StatusUnauthorized, w.Code, "Should return 401 Unauthorized")

		var response webapi.ErrorResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err, "Should be valid JSON")

		assert.Equal(t, "error", response.Status, "Response should indicate failure")
	})

	t.Run("get profile with invalid token", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodGet, "/api/v1/auth/profile", nil)
		req.Header.Set("Authorization", "Bearer invalid-token")

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verify error response
		assert.Equal(t, http.StatusUnauthorized, w.Code, "Should return 401 Unauthorized")

		var response webapi.ErrorResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err, "Should be valid JSON")

		assert.Equal(t, "error", response.Status, "Response should indicate failure")
	})

	t.Run("logout with valid token", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodPost, "/api/v1/auth/logout", nil)
		req.Header.Set("Authorization", "Bearer "+tokenResponse.AccessToken)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verify response
		assert.Equal(t, http.StatusOK, w.Code, "Should return 200 OK")

		var response webapi.SuccessResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err, "Should be valid JSON")

		assert.Equal(t, "success", response.Status, "Response should be successful")

		// Verify logout message
		logoutData := response.Data.(map[string]interface{})
		assert.Equal(t, "Logout successful", logoutData["message"])
	})

	t.Run("logout without token", func(t *testing.T) {
		req := httptest.NewRequest(http.MethodPost, "/api/v1/auth/logout", nil)
		// No Authorization header

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verify error response
		assert.Equal(t, http.StatusUnauthorized, w.Code, "Should return 401 Unauthorized")

		var response webapi.ErrorResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err, "Should be valid JSON")

		assert.Equal(t, "error", response.Status, "Response should indicate failure")
	})

	t.Run("access after logout should fail", func(t *testing.T) {
		// Try to access profile after logout (token should be invalidated)
		req := httptest.NewRequest(http.MethodGet, "/api/v1/auth/profile", nil)
		req.Header.Set("Authorization", "Bearer "+tokenResponse.AccessToken)

		w := httptest.NewRecorder()
		router.ServeHTTP(w, req)

		// Verify error response (token should be invalidated after logout)
		assert.Equal(t, http.StatusUnauthorized, w.Code, "Should return 401 Unauthorized after logout")

		var response webapi.ErrorResponse
		err = json.Unmarshal(w.Body.Bytes(), &response)
		require.NoError(t, err, "Should be valid JSON")

		assert.Equal(t, "error", response.Status, "Response should indicate failure")
	})
}

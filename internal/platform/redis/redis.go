package redis

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"

	"github.com/wongpinter/payment/internal/platform/config"
)

// NewClient creates a new Redis client with the provided configuration
// Rule 1.4: Function accepts interface where possible, returns concrete type
func NewClient(cfg *config.Config) (*redis.Client, error) {
	// Rule 1.2: camelCase variable names
	redisConfig := cfg.Redis

	// Create Redis client options
	opts := &redis.Options{
		Addr:         redisConfig.GetRedisAddress(),
		Password:     redisConfig.Password,
		DB:           redisConfig.DB,
		MaxRetries:   redisConfig.MaxRetries,
		PoolSize:     redisConfig.PoolSize,
		MinIdleConns: redisConfig.MinIdleConns,

		// Connection timeouts
		DialTimeout:  5 * time.Second,
		ReadTimeout:  3 * time.Second,
		WriteTimeout: 3 * time.Second,

		// Pool timeout
		PoolTimeout: 4 * time.Second,

		// Idle timeout
		ConnMaxIdleTime: 5 * time.Minute,
	}

	// Create the Redis client
	client := redis.NewClient(opts)

	// Test the connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("redis.NewClient: failed to connect to Redis: %w", err)
	}

	return client, nil
}

// HealthCheck performs a health check on the Redis connection
func HealthCheck(ctx context.Context, client *redis.Client) error {
	if err := client.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("redis.HealthCheck: %w", err)
	}
	return nil
}

// Close gracefully closes the Redis client connection
func Close(client *redis.Client) error {
	if err := client.Close(); err != nil {
		return fmt.Errorf("redis.Close: %w", err)
	}
	return nil
}

// SessionManager provides methods for managing user sessions in Redis
type SessionManager struct {
	client *redis.Client
}

// NewSessionManager creates a new session manager
func NewSessionManager(client *redis.Client) *SessionManager {
	return &SessionManager{
		client: client,
	}
}

// StoreRefreshToken stores a refresh token in Redis with the specified TTL
// Key format: refresh_token:<token>
// Value: user_id
func (sm *SessionManager) StoreRefreshToken(ctx context.Context, token, userID string, ttl time.Duration) error {
	key := fmt.Sprintf("refresh_token:%s", token)
	if err := sm.client.Set(ctx, key, userID, ttl).Err(); err != nil {
		return fmt.Errorf("sessionManager.StoreRefreshToken: %w", err)
	}
	return nil
}

// GetRefreshToken retrieves the user ID associated with a refresh token
func (sm *SessionManager) GetRefreshToken(ctx context.Context, token string) (string, error) {
	key := fmt.Sprintf("refresh_token:%s", token)
	userID, err := sm.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return "", fmt.Errorf("sessionManager.GetRefreshToken: token not found")
		}
		return "", fmt.Errorf("sessionManager.GetRefreshToken: %w", err)
	}
	return userID, nil
}

// DeleteRefreshToken removes a refresh token from Redis
func (sm *SessionManager) DeleteRefreshToken(ctx context.Context, token string) error {
	key := fmt.Sprintf("refresh_token:%s", token)
	if err := sm.client.Del(ctx, key).Err(); err != nil {
		return fmt.Errorf("sessionManager.DeleteRefreshToken: %w", err)
	}
	return nil
}

// StoreAccessTokenJTI stores an access token's JTI in Redis with the specified TTL
// Key format: session:<jti>
// Value: user_id
func (sm *SessionManager) StoreAccessTokenJTI(ctx context.Context, jti, userID string, ttl time.Duration) error {
	key := fmt.Sprintf("session:%s", jti)
	if err := sm.client.Set(ctx, key, userID, ttl).Err(); err != nil {
		return fmt.Errorf("sessionManager.StoreAccessTokenJTI: %w", err)
	}
	return nil
}

// ValidateAccessTokenJTI checks if an access token's JTI exists in Redis
func (sm *SessionManager) ValidateAccessTokenJTI(ctx context.Context, jti string) (string, error) {
	key := fmt.Sprintf("session:%s", jti)
	userID, err := sm.client.Get(ctx, key).Result()
	if err != nil {
		if err == redis.Nil {
			return "", fmt.Errorf("sessionManager.ValidateAccessTokenJTI: session not found")
		}
		return "", fmt.Errorf("sessionManager.ValidateAccessTokenJTI: %w", err)
	}
	return userID, nil
}

// DeleteAccessTokenJTI removes an access token's JTI from Redis
func (sm *SessionManager) DeleteAccessTokenJTI(ctx context.Context, jti string) error {
	key := fmt.Sprintf("session:%s", jti)
	if err := sm.client.Del(ctx, key).Err(); err != nil {
		return fmt.Errorf("sessionManager.DeleteAccessTokenJTI: %w", err)
	}
	return nil
}

// RevokeAllUserSessions removes all sessions for a specific user
// This is useful for logout from all devices functionality
func (sm *SessionManager) RevokeAllUserSessions(ctx context.Context, userID string) error {
	// Find all refresh tokens for this user
	refreshPattern := "refresh_token:*"
	refreshKeys, err := sm.client.Keys(ctx, refreshPattern).Result()
	if err != nil {
		return fmt.Errorf("sessionManager.RevokeAllUserSessions: failed to find refresh tokens: %w", err)
	}

	// Find all session JTIs for this user
	sessionPattern := "session:*"
	sessionKeys, err := sm.client.Keys(ctx, sessionPattern).Result()
	if err != nil {
		return fmt.Errorf("sessionManager.RevokeAllUserSessions: failed to find sessions: %w", err)
	}

	// Combine all keys and filter by user ID
	var keysToDelete []string

	// Check refresh tokens
	for _, key := range refreshKeys {
		val, err := sm.client.Get(ctx, key).Result()
		if err == nil && val == userID {
			keysToDelete = append(keysToDelete, key)
		}
	}

	// Check session JTIs
	for _, key := range sessionKeys {
		val, err := sm.client.Get(ctx, key).Result()
		if err == nil && val == userID {
			keysToDelete = append(keysToDelete, key)
		}
	}

	// Delete all keys for this user
	if len(keysToDelete) > 0 {
		if err := sm.client.Del(ctx, keysToDelete...).Err(); err != nil {
			return fmt.Errorf("sessionManager.RevokeAllUserSessions: failed to delete keys: %w", err)
		}
	}

	return nil
}

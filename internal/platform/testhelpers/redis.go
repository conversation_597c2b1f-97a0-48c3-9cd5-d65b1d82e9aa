package testhelpers

import (
	"context"
	"fmt"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/testcontainers/testcontainers-go"
	redisContainer "github.com/testcontainers/testcontainers-go/modules/redis"
	"github.com/testcontainers/testcontainers-go/wait"
)

// RedisContainer holds the test Redis container and client
type RedisContainer struct {
	Container *redisContainer.RedisContainer
	Client    *redis.Client
	cleanup   func()
}

// SetupTestRedis creates a Redis testcontainer and returns a client
// Following Rule 9.1: Use testcontainers-go for all integration tests
func SetupTestRedis(ctx context.Context) (*redis.Client, func(), error) {
	// Create Redis container
	// Following Rule 9.1: Using testcontainers-go according to latest documentation
	redisC, err := redisContainer.Run(ctx,
		"redis:7-alpine",
		testcontainers.WithWaitStrategy(
			wait.ForLog("Ready to accept connections").
				WithStartupTimeout(30*time.Second)),
	)
	if err != nil {
		return nil, nil, fmt.Errorf("testhelpers.SetupTestRedis: failed to start redis container: %w", err)
	}

	// Get connection string
	connStr, err := redisC.ConnectionString(ctx)
	if err != nil {
		redisC.Terminate(ctx)
		return nil, nil, fmt.Errorf("testhelpers.SetupTestRedis: failed to get connection string: %w", err)
	}

	// Parse Redis URL and create client
	opt, err := redis.ParseURL(connStr)
	if err != nil {
		redisC.Terminate(ctx)
		return nil, nil, fmt.Errorf("testhelpers.SetupTestRedis: failed to parse redis URL: %w", err)
	}

	client := redis.NewClient(opt)

	// Test connection
	if err := client.Ping(ctx).Err(); err != nil {
		client.Close()
		redisC.Terminate(ctx)
		return nil, nil, fmt.Errorf("testhelpers.SetupTestRedis: failed to ping redis: %w", err)
	}

	// Cleanup function
	cleanup := func() {
		if client != nil {
			client.Close()
		}
		if redisC != nil {
			redisC.Terminate(ctx)
		}
	}

	return client, cleanup, nil
}

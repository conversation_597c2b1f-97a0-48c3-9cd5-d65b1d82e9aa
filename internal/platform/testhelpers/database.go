package testhelpers

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/postgres"
	"github.com/testcontainers/testcontainers-go/wait"
)

// DatabaseContainer holds the test database container and connection
type DatabaseContainer struct {
	Container *postgres.PostgresContainer
	Pool      *pgxpool.Pool
	cleanup   func()
}

// SetupTestDatabase creates a PostgreSQL testcontainer, runs migrations, and seeds data
// Following Rule 9.1: Use testcontainers-go for all integration tests
func SetupTestDatabase(ctx context.Context) (*pgxpool.Pool, func(), error) {
	// Create PostgreSQL container
	// Following Rule 9.1: Using testcontainers-go according to latest documentation
	postgresContainer, err := postgres.Run(ctx,
		"postgres:16-alpine",
		postgres.WithDatabase("testdb"),
		postgres.WithUsername("testuser"),
		postgres.WithPassword("testpass"),
		testcontainers.WithWaitStrategy(
			wait.ForLog("database system is ready to accept connections").
				WithOccurrence(2).
				WithStartupTimeout(30*time.Second)),
	)
	if err != nil {
		return nil, nil, fmt.Errorf("testhelpers.SetupTestDatabase: failed to start postgres container: %w", err)
	}

	// Get connection string
	connStr, err := postgresContainer.ConnectionString(ctx, "sslmode=disable")
	if err != nil {
		postgresContainer.Terminate(ctx)
		return nil, nil, fmt.Errorf("testhelpers.SetupTestDatabase: failed to get connection string: %w", err)
	}

	// Create connection pool
	pool, err := pgxpool.New(ctx, connStr)
	if err != nil {
		postgresContainer.Terminate(ctx)
		return nil, nil, fmt.Errorf("testhelpers.SetupTestDatabase: failed to create connection pool: %w", err)
	}

	// Test connection
	if err := pool.Ping(ctx); err != nil {
		pool.Close()
		postgresContainer.Terminate(ctx)
		return nil, nil, fmt.Errorf("testhelpers.SetupTestDatabase: failed to ping database: %w", err)
	}

	// Run migrations
	if err := runMigrations(ctx, pool); err != nil {
		pool.Close()
		postgresContainer.Terminate(ctx)
		return nil, nil, fmt.Errorf("testhelpers.SetupTestDatabase: failed to run migrations: %w", err)
	}

	// Run seed data
	// Following Rule 9.2: Apply seed files after migrations
	if err := runSeeds(ctx, pool); err != nil {
		pool.Close()
		postgresContainer.Terminate(ctx)
		return nil, nil, fmt.Errorf("testhelpers.SetupTestDatabase: failed to run seeds: %w", err)
	}

	// Cleanup function
	cleanup := func() {
		if pool != nil {
			pool.Close()
		}
		if postgresContainer != nil {
			postgresContainer.Terminate(ctx)
		}
	}

	return pool, cleanup, nil
}

// runMigrations executes all migration files from the migrations directory
func runMigrations(ctx context.Context, pool *pgxpool.Pool) error {
	// Find the project root by looking for go.mod
	projectRoot, err := findProjectRoot()
	if err != nil {
		return fmt.Errorf("runMigrations: failed to find project root: %w", err)
	}

	migrationsDir := filepath.Join(projectRoot, "migrations")

	// Get all .up.sql files
	files, err := filepath.Glob(filepath.Join(migrationsDir, "*.up.sql"))
	if err != nil {
		return fmt.Errorf("runMigrations: failed to glob migration files: %w", err)
	}

	// Execute each migration file in order
	for _, file := range files {
		content, err := os.ReadFile(file)
		if err != nil {
			return fmt.Errorf("runMigrations: failed to read migration file %s: %w", file, err)
		}

		if _, err := pool.Exec(ctx, string(content)); err != nil {
			return fmt.Errorf("runMigrations: failed to execute migration %s: %w", file, err)
		}
	}

	return nil
}

// runSeeds executes all seed files from the testdata/seeds directory
// Following Rule 9.2: Execute seed files after migrations
func runSeeds(ctx context.Context, pool *pgxpool.Pool) error {
	// Find the project root by looking for go.mod
	projectRoot, err := findProjectRoot()
	if err != nil {
		return fmt.Errorf("runSeeds: failed to find project root: %w", err)
	}

	seedsDir := filepath.Join(projectRoot, "testdata", "seeds")

	// Check if seeds directory exists
	if _, err := os.Stat(seedsDir); os.IsNotExist(err) {
		// No seeds directory, skip seeding
		return nil
	}

	// Get all .sql files
	files, err := filepath.Glob(filepath.Join(seedsDir, "*.sql"))
	if err != nil {
		return fmt.Errorf("runSeeds: failed to glob seed files: %w", err)
	}

	// Execute each seed file in order
	for _, file := range files {
		content, err := os.ReadFile(file)
		if err != nil {
			return fmt.Errorf("runSeeds: failed to read seed file %s: %w", file, err)
		}

		if _, err := pool.Exec(ctx, string(content)); err != nil {
			return fmt.Errorf("runSeeds: failed to execute seed %s: %w", file, err)
		}
	}

	return nil
}

// findProjectRoot finds the project root directory by looking for go.mod
func findProjectRoot() (string, error) {
	dir, err := os.Getwd()
	if err != nil {
		return "", fmt.Errorf("findProjectRoot: failed to get working directory: %w", err)
	}

	// Walk up the directory tree looking for go.mod
	for {
		goModPath := filepath.Join(dir, "go.mod")
		if _, err := os.Stat(goModPath); err == nil {
			return dir, nil
		}

		parent := filepath.Dir(dir)
		if parent == dir {
			// Reached the root directory
			break
		}
		dir = parent
	}

	return "", fmt.Errorf("findProjectRoot: go.mod not found in any parent directory")
}

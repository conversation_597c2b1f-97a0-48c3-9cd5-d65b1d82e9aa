package testhelpers

import (
	"context"
	"testing"
	"time"
)

// TestSetupTestDatabase tests the database setup helper
// Following Rule 9.1: Use testcontainers-go for integration tests
func TestSetupTestDatabase(t *testing.T) {
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// Setup test database
	pool, cleanup, err := SetupTestDatabase(ctx)
	if err != nil {
		t.Fatalf("Failed to setup test database: %v", err)
	}
	defer cleanup()

	// Test that we can query the database
	var count int
	err = pool.QueryRow(ctx, "SELECT COUNT(*) FROM roles").Scan(&count)
	if err != nil {
		t.Fatalf("Failed to query roles table: %v", err)
	}

	// Should have seeded roles
	if count == 0 {
		t.<PERSON><PERSON><PERSON>("Expected seeded roles, but got 0")
	}

	t.Logf("Successfully set up test database with %d roles", count)
}

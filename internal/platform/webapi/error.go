package webapi

import (
	"github.com/getsentry/sentry-go"
	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
)

// APIError represents an API error
type APIError struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

// ErrorResponse represents an error API response
type ErrorResponse struct {
	Status string   `json:"status"`
	Error  APIError `json:"error"`
}

// ValidationErrorResponse represents a validation error response
type ValidationErrorResponse struct {
	Status string                 `json:"status"`
	Error  APIError               `json:"error"`
	Fields map[string]interface{} `json:"fields,omitempty"`
}

// Error sends an error response
func Error(c *gin.Context, status int, code, message string) {
	errResp := ErrorResponse{
		Status: "error",
		Error: APIError{
			Code:    code,
			Message: message,
		},
	}
	c.JSON(status, errResp)
}

// ErrorWithSentry sends an error response and captures the error in Sentry
func ErrorWithSentry(c *gin.Context, status int, err error) {
	// Capture the original error in Sentry
	sentry.CaptureException(err)
	log.Error().Err(err).Msg("An internal server error occurred")

	// Return a generic message to the user
	Error(c, status, "INTERNAL_SERVER_ERROR", "An unexpected error occurred. Please try again later.")
}

// ValidationError sends a validation error response
func ValidationError(c *gin.Context, status int, code, message string, fields map[string]interface{}) {
	errResp := ValidationErrorResponse{
		Status: "error",
		Error: APIError{
			Code:    code,
			Message: message,
		},
		Fields: fields,
	}
	c.JSON(status, errResp)
}

// BadRequest sends a bad request error response
func BadRequest(c *gin.Context, message string) {
	Error(c, 400, "BAD_REQUEST", message)
}

// Unauthorized sends an unauthorized error response
func Unauthorized(c *gin.Context, message string) {
	Error(c, 401, "UNAUTHORIZED", message)
}

// Forbidden sends a forbidden error response
func Forbidden(c *gin.Context, message string) {
	Error(c, 403, "FORBIDDEN", message)
}

// NotFound sends a not found error response
func NotFound(c *gin.Context, message string) {
	Error(c, 404, "NOT_FOUND", message)
}

// Conflict sends a conflict error response
func Conflict(c *gin.Context, message string) {
	Error(c, 409, "CONFLICT", message)
}

// InternalServerError sends an internal server error response
func InternalServerError(c *gin.Context, err error) {
	ErrorWithSentry(c, 500, err)
}

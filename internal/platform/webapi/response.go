package webapi

import (
	"github.com/gin-gonic/gin"
)

// SuccessResponse represents a successful API response
type SuccessResponse struct {
	Status string      `json:"status"`
	Data   interface{} `json:"data,omitempty"`
}

// PaginatedResponse represents a paginated API response
type PaginatedResponse struct {
	SuccessResponse
	Pagination interface{} `json:"pagination,omitempty"`
}

// PaginationInfo contains pagination metadata
type PaginationInfo struct {
	Page       int   `json:"page"`
	Limit      int   `json:"limit"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
	HasNext    bool  `json:"has_next"`
	HasPrev    bool  `json:"has_prev"`
}

// Success sends a successful response with data
func Success(c *gin.Context, status int, data interface{}) {
	c.J<PERSON>(status, SuccessResponse{
		Status: "success",
		Data:   data,
	})
}

// SuccessWithMessage sends a successful response with a message
func SuccessWithMessage(c *gin.Context, status int, message string) {
	c.J<PERSON>(status, SuccessResponse{
		Status: "success",
		Data:   gin.H{"message": message},
	})
}

// SuccessPaginated sends a successful paginated response
func SuccessPaginated(c *gin.Context, status int, data interface{}, pagination PaginationInfo) {
	c.JSON(status, PaginatedResponse{
		SuccessResponse: SuccessResponse{
			Status: "success",
			Data:   data,
		},
		Pagination: pagination,
	})
}

// NewPaginationInfo creates pagination metadata
func NewPaginationInfo(page, limit int, total int64) PaginationInfo {
	totalPages := int((total + int64(limit) - 1) / int64(limit))
	if totalPages == 0 {
		totalPages = 1
	}

	return PaginationInfo{
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}
}

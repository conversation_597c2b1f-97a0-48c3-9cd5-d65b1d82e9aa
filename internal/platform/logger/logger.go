package logger

import (
	"os"
	"strings"
	"time"

	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

// Init initializes the global logger with the specified level and mode
func Init(level, mode string) {
	// Parse log level
	logLevel, err := zerolog.ParseLevel(strings.ToLower(level))
	if err != nil {
		logLevel = zerolog.InfoLevel
	}
	zerolog.SetGlobalLevel(logLevel)

	// Configure output format based on mode
	if mode == "development" {
		// Use beautiful, human-readable console output for development
		log.Logger = log.Output(zerolog.ConsoleWriter{
			Out:        os.Stderr,
			TimeFormat: time.RFC3339,
		})
	} else {
		// Use structured JSON for production for easy parsing by log aggregators
		log.Logger = zerolog.New(os.Stderr).With().Timestamp().Logger()
	}
}

// GetLogger returns a logger instance with the specified component name
func GetLogger(component string) zerolog.Logger {
	return log.With().Str("component", component).Logger()
}

// GetLoggerWithFields returns a logger instance with custom fields
func GetLoggerWithFields(fields map[string]interface{}) zerolog.Logger {
	logger := log.Logger
	for key, value := range fields {
		logger = logger.With().Interface(key, value).Logger()
	}
	return logger
}

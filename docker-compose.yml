version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - PAYMENT_SERVER_MODE=development
      - PAYMENT_DATABASE_HOST=db
      - PAYMENT_DATABASE_PORT=5432
      - PAYMENT_DATABASE_USER=payment_user
      - PAYMENT_DATABASE_PASSWORD=payment_password
      - PAYMENT_DATABASE_NAME=payment_db
      - PAYMENT_DATABASE_SSL_MODE=disable
      - PAYMENT_REDIS_HOST=redis
      - PAYMENT_REDIS_PORT=6379
      - PAYMENT_LOGGER_LEVEL=debug
      - PAYMENT_LOGGER_MODE=development
      - SENTRY_DSN=${SENTRY_DSN:-}
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - payment-network
    restart: unless-stopped

  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_USER=payment_user
      - POSTGRES_PASSWORD=payment_password
      - POSTGRES_DB=payment_db
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U payment_user -d payment_db"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - payment-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    networks:
      - payment-network
    restart: unless-stopped

  # Optional: Database migration service
  migrate:
    image: migrate/migrate
    profiles: ["tools"]
    volumes:
      - ./migrations:/migrations
    command: [
      "-path", "/migrations",
      "-database", "************************************************/payment_db?sslmode=disable",
      "up"
    ]
    depends_on:
      db:
        condition: service_healthy
    networks:
      - payment-network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  payment-network:
    driver: bridge
